#include <Arduino.h>
#include <EEPROM.h>
#include <CRC.h>
#include <jm_crc-ccitt.h>
#include <PID_v1.h>
#include <avr/wdt.h>
#include <avr/sleep.h>
#include <math.h>
#include <string.h>
#include <Wire.h>
#include <Adafruit_ADS1X15.h>
#include <PZEM004Tv30.h>

// --- Project headers ---
#include "Simulation.h"
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"
#include "ELRegulator.h"
#include "SafetyController.h"
#include "SystemController.h"
#include "ElectrolyzerController.h"
#include "FuelCellController.h"
#include "TelemetryController.h"
#include "DisplayUtils.h"
#include "Controllers.h"

// --- Global variables ---
// Control flags (in Constants namespace)
// namespace Constants {
//   bool IsHeaterEnabled = false; // Default disabled, can be controlled via telemetry/menu
//   bool IsVoltageCheckEnabled = false; // Default disabled, can be controlled via telemetry/menu
//   bool IsPSUChecksEnabled = false; // Default disabled, can be controlled via telemetry/menu
// }

// External references
extern ELRegulator elRegulator;

// --- Global actuator arrays ---
const uint8_t ACTUATOR_PINS[ACTUATOR_PIN_COUNT] = {
   ACT_FC_POWER_RELAY,
   ACT_WATER_PUMP_PM_02,
   ACT_WATER_INLET_VALVE_SV_01,
   ACT_EL_HEATER_EH_01,
   ACT_H2_OUTPUT_VALVE_SV_03,
   ACT_O2_OUTPUT_VALVE_SV_02,
   ACT_H2_DRYER_DISCHARGE_VALVE_SV_04,
   ACT_EL_O2_CHILLER_CF_01,
   ACT_WATER_FILL_PUMP_PM_01,
   ACT_FC_H2_SUPPLY_VALVE_SV_05,
   ACT_FC_O2_SUPPLY_VALVE_SV_06,
   ACT_FC_O2_FAN_RF_01,
   ACT_FC_H2_DISCHARGE_VALVE_SV_07,
   ACT_FC_O2_CHILLER_CF_02,
   ACT_FC_O2_DRYER_PUMP_PM_03,
   ACT_FC_LOAD_RELAY,
   ACT_BMS_CHARGE_RELAY,
   ACT_BMS_DISCHARGE_RELAY,
   ACT_INVERTER_RELAY,
   ACT_EMERGENCY_VENT,
   ACT_MAIN_INPUT_RELAY,
   ACT_MAIN_BMS_OUT_RELAY,
   ACT_EL_PSU_RELAY
};

const uint8_t DIGITAL_PINS[3] = {
   PIN_SENSOR_FIRE_DETECT_FS_01,
   PIN_SENSOR_H2_AMBIENT_HS_01,
   PIN_EMERGENCY_STOP_ES_01
};

const uint8_t ANALOG_PINS[3] = {
   PIN_ELEC_O2_DRYER_LEVEL_LS_02,
   PIN_ELEC_H2_DRYER_LEVEL_LS_03,
   PIN_FC_O2_DRYER_LEVEL_LS_04,
};

const char* actuatorNames[ACTUATOR_PIN_COUNT] = {
   "FuelCell PSU Relay",
   "Water Pump", "Water Inlet Valve", "Electrical Heater",
   "H2 Output Valve", "O2 Output Valve", "H2 Dryer Discharge Valve",
   "EL O2 Chiller Relay", "Elec O2 Dryer Water Inlet Pump",
   "FC H2 Supply Valve", "FC O2 Supply Valve", "FC O2 Fan",
   "FC H2 Discharge Valve", "FC O2 Chiller Relay", "FC O2 Dryer Pump",
   "FC Load Relay", "BMS Charge Relay", "BMS Discharge Relay",
   "Inverter Relay", "Emergency Vent", "Main Input Relay", "Main BMS Out Relay",
   "Electrolyzer PSU Relay"
};

// --- Timing variables ---
unsigned long lastControlUpdateMs = 0;
unsigned long lastTelemetryMs = 0;
unsigned long lastWatchdogResetMs = 0;
unsigned long lastElectrolyzerModeCheck = 0;

// --- External instances ---
extern ELRegulator elRegulator;
extern SafetyController safety_controller;

bool isElEnabled = false;

// --- Arduino setup() ---
void setup() {
	// MOSFET-driven valves/pumps (LOW = OFF)
	pinMode(ACT_WATER_INLET_VALVE_SV_01, OUTPUT); digitalWrite(ACT_WATER_INLET_VALVE_SV_01, LOW);
	pinMode(ACT_EL_HEATER_EH_01, OUTPUT); digitalWrite(ACT_EL_HEATER_EH_01, LOW);
	pinMode(ACT_H2_OUTPUT_VALVE_SV_03, OUTPUT); digitalWrite(ACT_H2_OUTPUT_VALVE_SV_03, LOW);
	pinMode(ACT_O2_OUTPUT_VALVE_SV_02, OUTPUT); digitalWrite(ACT_O2_OUTPUT_VALVE_SV_02, LOW);
	pinMode(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, OUTPUT); digitalWrite(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, LOW);
	pinMode(ACT_EL_O2_CHILLER_CF_01, OUTPUT); digitalWrite(ACT_EL_O2_CHILLER_CF_01, LOW);
	pinMode(ACT_FC_H2_SUPPLY_VALVE_SV_05, OUTPUT); digitalWrite(ACT_FC_H2_SUPPLY_VALVE_SV_05, LOW);
	pinMode(ACT_FC_O2_SUPPLY_VALVE_SV_06, OUTPUT); digitalWrite(ACT_FC_O2_SUPPLY_VALVE_SV_06, LOW);
	pinMode(ACT_FC_O2_FAN_RF_01, OUTPUT); digitalWrite(ACT_FC_O2_FAN_RF_01, LOW);
	pinMode(ACT_FC_H2_DISCHARGE_VALVE_SV_07, OUTPUT); digitalWrite(ACT_FC_H2_DISCHARGE_VALVE_SV_07, LOW);
	pinMode(ACT_FC_O2_CHILLER_CF_02, OUTPUT); digitalWrite(ACT_FC_O2_CHILLER_CF_02, LOW);
	pinMode(ACT_FC_O2_DRYER_PUMP_PM_03, OUTPUT); digitalWrite(ACT_FC_O2_DRYER_PUMP_PM_03, LOW);

	// Relays (HIGH = OFF)
	pinMode(ACT_FC_POWER_RELAY, OUTPUT); digitalWrite(ACT_FC_POWER_RELAY, HIGH);
	pinMode(ACT_WATER_FILL_PUMP_PM_01, OUTPUT); digitalWrite(ACT_WATER_FILL_PUMP_PM_01, HIGH);
	pinMode(ACT_WATER_PUMP_PM_02, OUTPUT); digitalWrite(ACT_WATER_PUMP_PM_02, HIGH);
	pinMode(ACT_FC_LOAD_RELAY, OUTPUT); digitalWrite(ACT_FC_LOAD_RELAY, HIGH);
	pinMode(ACT_BMS_CHARGE_RELAY, OUTPUT); digitalWrite(ACT_BMS_CHARGE_RELAY, HIGH);
	pinMode(ACT_BMS_DISCHARGE_RELAY, OUTPUT); digitalWrite(ACT_BMS_DISCHARGE_RELAY, HIGH);
	pinMode(ACT_INVERTER_RELAY, OUTPUT); digitalWrite(ACT_INVERTER_RELAY, HIGH);
	pinMode(ACT_EMERGENCY_VENT, OUTPUT); digitalWrite(ACT_EMERGENCY_VENT, HIGH);
	pinMode(ACT_MAIN_INPUT_RELAY, OUTPUT); digitalWrite(ACT_MAIN_INPUT_RELAY, HIGH);
	pinMode(ACT_MAIN_BMS_OUT_RELAY, OUTPUT); digitalWrite(ACT_MAIN_BMS_OUT_RELAY, HIGH);
	pinMode(ACT_EL_PSU_RELAY, OUTPUT); digitalWrite(ACT_EL_PSU_RELAY, HIGH);

	// Debug & telemetry serials
	DEBUG_SERIAL.begin(115200);
	while (!DEBUG_SERIAL && millis() < 3000)
	{ /* wait for USB */
	}
	TELEMETRY_SERIAL.begin(115200);

	Serial.println(F("Elektrolizor System Starting..."));

	// I2C for ADS1115, etc.
	Wire.begin();

	// Digital inputs
	pinMode(PIN_SENSOR_FIRE_DETECT_FS_01, INPUT_PULLUP);
	pinMode(PIN_SENSOR_H2_AMBIENT_HS_01, INPUT_PULLUP);
	pinMode(PIN_EMERGENCY_STOP_ES_01, INPUT_PULLUP);

	//Analog Inputs   
	pinMode(PIN_ELEC_O2_DRYER_LEVEL_LS_02, INPUT);
	pinMode(PIN_ELEC_H2_DRYER_LEVEL_LS_03, INPUT);
	pinMode(PIN_FC_O2_DRYER_LEVEL_LS_04, INPUT);

	// Initialize simulation FIRST if in simulation mode
	if (simulationMode) {
		initializeSimulation();
		Serial.println(F("Simulation mode initialized"));
	}

	// Initialize subsystems AFTER simulation
	initializeSensors();

	elRegulator.setOutputVoltage(0.0f);


	updateADSReadings();
	float _lvl_LS01 = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL_LS_01, CAL_WATER_LEVEL); // LS-01: Main water tank (Ana su tankı)
	char logBuf[100];
	snprintf(logBuf, sizeof(logBuf), "Water Level=%.1f %", _lvl_LS01);
	logMessage(LOG_INFO, F("System initialization complete."));
	delay(500);

	// Enable watchdog
	wdt_enable(WDTO_2S);

	delay(1000);
}

// --- Arduino loop() ---
void loop()
{
	unsigned long now = millis();

	if (!isElEnabled && now - lastElectrolyzerModeCheck >= 2000)
	{
		lastElectrolyzerModeCheck = now;
		isElEnabled = true;
		system_controller.request_mode(SystemController::MODE_ELECTROLYZER);

		logMessage(LOG_ERROR, F("Mode change to electrolyzer"));
	}

	// PRS-8: EMERGENCY STOP (highest priority)
	if (GetDigitalInputVal(PIN_EMERGENCY_STOP_ES_01) == LOW && !SIM_MODE_INITIAL)
	{
		// PRS-8: Emergency stop - set all actuators to startup states
		logMessage(LOG_ERROR, F("PRS-8: EMERGENCY STOP button pressed!"));
		logMessage(LOG_ERROR, F("PRS-8: Resetting all actuators to startup states"));

		// Set ALL actuators to their startup (safe) states
		// All pumps OFF
		SetDigitalOutputVal(ACT_WATER_PUMP_PM_02, LOW);                 // Water pump OFF
		SetDigitalOutputVal(ACT_WATER_FILL_PUMP_PM_01, LOW);            // Water fill pump OFF
		SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP_PM_03, LOW);           // FC O2 dryer pump OFF

		// All valves CLOSED
		SetDigitalOutputVal(ACT_WATER_INLET_VALVE_SV_01, LOW);          // Water inlet valve CLOSED
		SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE_SV_03, LOW);            // H2 output valve CLOSED
		SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE_SV_02, LOW);            // O2 output valve CLOSED
		SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, LOW);   // H2 dryer discharge CLOSED
		SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE_SV_05, LOW);         // FC H2 supply valve CLOSED
		SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE_SV_06, LOW);         // FC O2 supply valve CLOSED
		SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE_SV_07, LOW);      // FC H2 discharge CLOSED

		// All equipment OFF
		SetDigitalOutputVal(ACT_EL_HEATER_EH_01, LOW);                  // Heater OFF
		SetDigitalOutputVal(ACT_EL_O2_CHILLER_CF_01, LOW);              // Electrolyzer chiller OFF
		SetDigitalOutputVal(ACT_FC_O2_CHILLER_CF_02, LOW);              // FC chiller OFF
		SetDigitalOutputVal(ACT_FC_O2_FAN_RF_01, LOW);                  // FC O2 fan OFF

		// Power systems to safe states
		SetDigitalOutputVal(ACT_FC_POWER_RELAY, LOW);                     // Fuel Cell PSU OFF
		SetDigitalOutputVal(ACT_EL_PSU_RELAY, LOW);                     // Electrolyzer PSU OFF
		SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);                 // Main input power OFF
		SetDigitalOutputVal(ACT_FC_LOAD_RELAY, LOW);                    // FC load relay OFF
		SetDigitalOutputVal(ACT_BMS_CHARGE_RELAY, LOW);                 // BMS charge relay OFF
		SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, LOW);              // BMS discharge relay OFF
		SetDigitalOutputVal(ACT_INVERTER_RELAY, LOW);                   // Inverter relay OFF

		// Switch to battery power for essential systems
		SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, HIGH);              // Battery power ON

		// Emergency vent open for safety
		SetDigitalOutputVal(ACT_EMERGENCY_VENT, HIGH);                  // Emergency vent OPEN

		// Turn off electrolyzer regulator
		elRegulator.setOutputVoltage(0.0f);

		// Trigger safety controller and subsystem emergency stops
		safety_controller.triggerEmergency();
		electrolyzer_controller.emergency_stop();
		fuel_cell_controller.emergency_stop();

		// Log completion of emergency stop procedure
		logMessage(LOG_ERROR, F("PRS-8: Emergency stop procedure completed - System in safe state"));

		return;
	}

	// Process incoming telemetry/commands
	telemetry_controller.processIncomingCommands(TELEMETRY_SERIAL);

	// Control update at fixed frequency
	if (now - lastControlUpdateMs >= Constants::CONTROL_LOOP_FREQ_MS)
	{
		lastControlUpdateMs = now;
		// Debug: Show control loop timing (every 10 seconds)
		static unsigned long lastTimingDebug = 0;
		if (now - lastTimingDebug >= 10000) {
			Serial.print(F("DEBUG: Control loop running, interval="));
			Serial.print(now - lastTimingDebug);
			Serial.println(F("ms"));
			lastTimingDebug = now;
		}

		// Refresh ADS readings
		updateADSReadings();

		// Trend buffers (optional)
		float waterTemp = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_TEMP_TT_01, CAL_WATER_TEMP);
		float elecTemp = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP_TT_02, CAL_ELEC_TEMP);
		float bmsVolt = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_VOLTAGE_VBMS, CAL_BMS_VOLTAGE);
		waterTempBuffer.addSample(waterTemp);
		elecTempBuffer.addSample(elecTemp);
		bmsTempBuffer.addSample(bmsVolt);

		// Update safety controller first (highest priority)
		safety_controller.update();

		// Update system mode
		system_controller.update();

		//DisplayUtils::showError("MODE_ELECTROLYZER UPDATE");

		electrolyzer_controller.update();

		// //Mode-specific controllers
		//switch (system_controller.get_mode())
		//{
		//	case SystemController::MODE_ELECTROLYZER:
		//		electrolyzer_controller.update();
		//		break;
		//	case SystemController::MODE_FUEL_CELL:
		//		fuel_cell_controller.update();
		//		break;
		//	default:
		//		// safe / battery / test modes
		//	break;
		//}
	}

	// Telemetry send
	if (now - lastTelemetryMs >= Constants::TELEMETRY_INTERVAL_MS)
	{
		lastTelemetryMs = now;
		telemetry_controller.sendTelemetry(TELEMETRY_SERIAL);
		safety_controller.resetCommWatchdog();
	}

	// Watchdog reset
	if (now - lastWatchdogResetMs >= Constants::WATCHDOG_RESET_INTERVAL_MS)
	{
		wdt_reset();
		lastWatchdogResetMs = now;
	}


}