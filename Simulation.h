#ifndef SIMULATION_H
#define SIMULATION_H

/**
 * Simulation.h
 * Provides simulation capabilities for testing the system without hardware
 * Defines simulation values, models, and state variables
 */

#include <Arduino.h>
#include "Constants.h"

 /**
  * Simulation values for sensors
  * Raw values are scaled to produce realistic calibrated readings
  */
namespace SimulationValues {
	// Raw simulation values for analog/ADS sensors.
	// The raw value is chosen so that after calibration
	// (i.e. (raw + offset)*scale) the sensor returns the desired value.
	// For example, ELECTROLIZOR_TEMP_RAW is set to (30°C/80) so that:
	//    calibrated value = (30/80)*80 = 30°C.
	extern const float WATER_LEVEL_RAW;
	extern const float WATER_TEMP_RAW;
	extern const float WATER_PRESS_RAW;
	extern const float ELEC_VOLTAGE;
	extern const float ELEC_CURRENT_RAW;
	extern const float ELECTROLIZOR_TEMP_RAW;
	extern const float ELECTROLIZOR_PRESS_RAW;
	extern const float H2_TANK_TEMP_RAW;
	extern const float H2_TANK_PRESS_RAW;
	extern const float O2_TANK_TEMP_RAW;
	extern const float O2_TANK_PRESS_RAW;
	extern const float FC_VOLTAGE;
	extern const float FC_CURRENT_RAW;
	extern const float BMS_VOLTAGE_RAW;
	extern const float BMS_CURRENT_RAW;
}

// PZEM simulation model
struct PZEMModel {
	float voltage;
	float current;
	float power;
	float energy;
	float frequency;
	float pf;
};

// Digital input simulation
struct DigitalInputSimulation {
	bool sensorFireDetect;    // PIN_SENSOR_FIRE_DETECT_FS_01
	bool sensorH2Ambient;     // PIN_SENSOR_H2_AMBIENT_HS_01
	bool buttonEmergencyStop; // PIN_EMERGENCY_STOP_ES_01
};

struct AnalogInputSimulation {
	uint8_t elecO2DryerLevel;    // PIN_ELEC_O2_DRYER_LEVEL_LS_02
	uint8_t elecH2DryerLevel;    // PIN_ELEC_H2_DRYER_LEVEL_LS_03
	uint8_t fcO2DryerLevel;      // PIN_FC_O2_DRYER_LEVEL_LS_04
};

// Global simulation variables
extern bool simulationMode;
extern float analogSensors[NUM_ANALOG_SENSORS];
extern float adsSensors[14];
extern float currentACS_EL_SimValue;
extern float currentACS_BMS_SimValue;
extern float currentACS_FC_SimValue;
extern float volt_EL_SimValue;
extern float volt_FC_SimValue;
extern float volt_BMS_SimValue;
extern PZEMModel pzemModel[NUM_PZEMS];
extern DigitalInputSimulation simDigitalInputs;
extern AnalogInputSimulation simAnalogInputs;
extern bool simDigitalOutputs[ACTUATOR_PIN_COUNT];

// Initialize simulation values
void initializeSimulation();

#endif // SIMULATION_H
