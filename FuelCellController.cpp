#include "FuelCellController.h"
#include "SafetyController.h" // defines safety_controller
#include "Sensors.h" // GetDigitalInputVal(), SetDigitalOutputVal()
#include "DisplayUtils.h" // DisplayUtils::showState(), showError()

extern SafetyController safety_controller;

// Global FuelCellController instance
FuelCellController fuel_cell_controller;

FuelCellController::FuelCellController()
	: _press_PT02(0),
	_press_PT03(0),
	_temp_TT03(0),
	_temp_TT04(0),
	_state(IDLE),
	_tState(0),
	_tLastAction(0),
	_sv07Open(false),
	_sv07Ts(0),
	_fcO2DryerActive(false),
	_lastFcO2DryerToggle(0),
	_tRunStart(0)
{
}

void FuelCellController::begin() {
	reset();
}

void FuelCellController::reset() {
	_state = PRS4_POWER_CHECK;
	_tState = millis();
	_tLastAction = millis();
	_sv07Open = false;
	_fcO2DryerActive = false;
	_lastFcO2DryerToggle = 0;
	_tRunStart = 0;
	DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "reset", "FuelCellController", stateToString(_state), "{}");
}

void FuelCellController::update() {
	// Emergency override
	if (_state != EMERGENCY && safety_controller.getFaultStatus() != 0) {
		_state = EMERGENCY;
		_tState = millis();
		emergency_stop();
		return;
	}

	readAllSensors();

	// Always handle LS-04 dryer control (integrated from FuelCellDryerController)
	handleFcO2Dryer();

	switch (_state) {
	case PRS4_POWER_CHECK:
	case PRS4_GAS_CHECK:
		performPRS4();
		break;
	case PRS5_RUN:
		performPRS5();
		break;
	case PRS6_SHUTDOWN:
		performPRS6();
		break;
	case EMERGENCY:
		emergency_stop();
		break;
	default:
		break;
	}
}

void FuelCellController::readAllSensors() {
	_press_PT02 = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_PRESS_PT_02, CAL_H2_TANK_PRESS);
	_press_PT03 = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_PRESS_PT_03, CAL_O2_TANK_PRESS);
	_temp_TT03 = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_TEMP_TT_03, CAL_H2_TANK_TEMP);
	_temp_TT04 = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_TEMP_TT_04, CAL_O2_TANK_TEMP);
}

void FuelCellController::performPRS4() {
	auto now = millis();

	if (_state == PRS4_POWER_CHECK) {
		// PRS-4: System power check per procedure 3.2.1
		bool powerOk = true;

		// Todo: bms power check
		// Batarya güç seviyesinin kontrol edileceği ve %20'nin altında ise "Yakıt hücresi çalıştırılamaz! Batarya seviyesi düşük." uyarısı verileceği
		
		// Fuel cell voltage checking is done during PRS5_RUN state, not during startup
		// Power check during startup only verifies system readiness

		// Fuel cell does not use AC power - it uses BMS battery power via ACT_MAIN_BMS_OUT_RELAY
		// No PSU checks needed for fuel cell operation

		if (powerOk) {
			_state = PRS4_GAS_CHECK;
			_tState = now;
			DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "performPRS4", "FuelCellController", stateToString(_state), "{}");
		}
		else {
			DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS4", "FuelCellController", "Sistem guc degerleri uygun degildir!");
			safety_controller.reportSubsystemFault(SafetyController::FAULT_SELFTEST_FAIL);
		}
	}
	else { // PRS4_GAS_CHECK
		// PRS-4: Gas tank pressure check per procedure 3.2.2
		// Required gas pressure: 0.5–4 barg for fuel cell operation
		// H2 pressure ≤0.5 barg: "Yakıt Hücresi Çalıştırılamaz, Hidrojen Tankı Boş!"
		if (_press_PT02 > Constants::MIN_TANK_PRESSURE_BAR && _press_PT02 <= Constants::MAX_TANK_PRESSURE_BAR &&
			_press_PT03 > Constants::MIN_TANK_PRESSURE_BAR && _press_PT03 <= Constants::MAX_TANK_PRESSURE_BAR)
		{
			_state = PRS5_RUN;
			_tState = now;
			_tLastAction = now;

			// Enhanced logging for procedure compliance
			logMessage(LOG_INFO, F("PRS-4 completed successfully - Starting PRS-5 fuel cell operation"));
			char logBuf[100];
			snprintf(logBuf, sizeof(logBuf), "Gas pressures: H2=%.1f bar, O2=%.1f bar", _press_PT02, _press_PT03);
			logMessage(LOG_INFO, F("PRS-4 gas check: "), String(logBuf));

			DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "performPRS4", "FuelCellController", stateToString(_state), "{}");
		}
		else {
			if (_press_PT02 <= Constants::MIN_TANK_PRESSURE_BAR) {
				DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS4", "FuelCellController", "Yakit Hucresi Calistirilamaz, Hidrojen Tanki Bos!");
			}
			else {
				DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS4", "FuelCellController", "Tank basinclari uygun degil, baslatilamadi!");
			}
			safety_controller.reportSubsystemFault(SafetyController::FAULT_OVERPRESSURE);
		}
	}
}

void FuelCellController::performPRS5() {
	auto now = millis();
	// Debounce between actuator steps
	if (now - _tLastAction < Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL) return;

	// Set run start time when first entering PRS5_RUN
	if (_tRunStart == 0) {
		_tRunStart = now;
	}

	//Todo: check relay
	// Step 0: open FC_PWR (Power supply to controller & Fan)
	SetDigitalOutputVal(ACT_FC_POWER_RELAY, HIGH);
	delay(50);

	// Step 1: open SV-05 (H2 supply)
	SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE_SV_05, HIGH);
	delay(50);

	// Step 2: open SV-06 (O2 supply)
	SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE_SV_06, HIGH);
	delay(50);

	// Step 3: enable CF-02 (O2 chiller)
	SetDigitalOutputVal(ACT_FC_O2_CHILLER_CF_02, HIGH);

	// Now running: display temperatures
	char buf[64];
	snprintf(buf, sizeof(buf), "T H2=%.1fC O2=%.1fC", _temp_TT03, _temp_TT04);
	DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "performPRS5", "FuelCellController", stateToString(_state), buf);

	// Voltage checking: only after 10 seconds of running and if enabled
	if (Constants::IsVoltageCheckEnabled && (now - _tRunStart >= 10000)) {
		float voltFC = getVolt_FC();
		if (!(voltFC >= Constants::MIN_OPERATING_VOLTAGE && voltFC <= Constants::MAX_OPERATING_VOLTAGE)) {
			DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS5", "FuelCellController", "Yakit hucresi voltaj degerleri uygun degildir!");
			safety_controller.reportSubsystemFault(SafetyController::FAULT_SELFTEST_FAIL);
			_state = PRS6_SHUTDOWN;
			_tState = now;
			_sv07Open = false;
			return;
		}
	}

	// PRS-5: Monitor for low H2 pressure shutdown trigger per procedure 5.3
	if (_press_PT02 <= Constants::FC_H2_SHUTDOWN_PRESSURE) {
		_state = PRS6_SHUTDOWN;
		_tState = now;
		_sv07Open = false;
		DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS5", "FuelCellController", "H2 azaldi, durduruyorum.");
	}
	// Warning when H2 pressure drops to 1 barg per procedure 5.3
	else if (_press_PT02 <= Constants::FC_H2_WARNING_PRESSURE) {
		DisplayUtils::showWarning(CMD_MODE_CHANGE_REQUEST, "performPRS5", "FuelCellController", "Hidrojen Gazi Miktari Azalmistir!");
	}
}

void FuelCellController::performPRS6() {
	auto now = millis();

	// PRS-6: Fuel cell shutdown procedure per procedure 5.3
	// Step 1: Close actuators in sequence per procedure 5.3
	// 1. SV-05 (H2 supply valve)
	// 2. SV-06 (O2 supply valve)
	// 3. CF-02 (O2 chiller)
	SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE_SV_05, LOW);
	SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE_SV_06, LOW);
	SetDigitalOutputVal(ACT_FC_O2_CHILLER_CF_02, LOW);

	// Step 2: Open SV-07 (gas discharge) for exactly 10 seconds per procedure
	if (!_sv07Open) {
		SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE_SV_07, HIGH);
		_sv07Open = true;
		_sv07Ts = now;
	}
	else if (now - _sv07Ts >= Constants::FC_H2_DISCHARGE_TIME) {
		SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE_SV_07, LOW);
		_sv07Open = false;

		// Final Actuator: close FC_PWR (Power supply to controller & Fan)
		SetDigitalOutputVal(ACT_FC_POWER_RELAY, LOW);
		_tRunStart = 0;  // Reset run start time
		_state = STOPPED;

		// Enhanced logging for procedure compliance
		logMessage(LOG_INFO, F("PRS-6 shutdown procedure completed successfully"));
		char logBuf[100];
		snprintf(logBuf, sizeof(logBuf), "Final gas pressures: H2=%.1f bar, O2=%.1f bar", _press_PT02, _press_PT03);
		logMessage(LOG_INFO, F("PRS-6 final state: "), String(logBuf));

		DisplayUtils::showState(stateToString(_state), "{}");
		DisplayUtils::showError("Yakit Hucre kapatildi!");
	}
}

void FuelCellController::deactivate() {
	// Safe shutdown - same as PRS6 but without discharge
	SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE_SV_05, LOW);
	SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE_SV_06, LOW);
	SetDigitalOutputVal(ACT_FC_O2_CHILLER_CF_02, LOW);
	// Final Actuator: close FC_PWR (Power supply to controller & Fan)
	SetDigitalOutputVal(ACT_FC_POWER_RELAY, LOW);
	_tRunStart = 0;  // Reset run start time
	_state = STOPPED;
	DisplayUtils::showState(stateToString(_state), "{}");
}

void FuelCellController::emergency_stop() {
	// Emergency stop: Reset ALL actuators to startup states (not just fuel cell actuators)
	// This is necessary because actuators may be manually enabled via PC telemetry

	// All pumps OFF
	SetDigitalOutputVal(ACT_WATER_PUMP_PM_02, LOW);
	SetDigitalOutputVal(ACT_WATER_FILL_PUMP_PM_01, LOW);
	SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP_PM_03, LOW);

	// All valves CLOSED
	SetDigitalOutputVal(ACT_WATER_INLET_VALVE_SV_01, LOW);
	SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE_SV_03, LOW);
	SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE_SV_02, LOW);
	SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, LOW);
	SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE_SV_05, LOW);
	SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE_SV_06, LOW);
	SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE_SV_07, LOW);

	// All equipment OFF
	SetDigitalOutputVal(ACT_EL_HEATER_EH_01, LOW);
	SetDigitalOutputVal(ACT_EL_O2_CHILLER_CF_01, LOW);
	SetDigitalOutputVal(ACT_FC_O2_CHILLER_CF_02, LOW);
	//TODO: USE RF_01 IN CLOSED SYSTEM
	SetDigitalOutputVal(ACT_FC_O2_FAN_RF_01, LOW);

	// Power systems to safe states
	SetDigitalOutputVal(ACT_EL_PSU_RELAY, LOW);
	SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);
	SetDigitalOutputVal(ACT_FC_LOAD_RELAY, LOW);
	SetDigitalOutputVal(ACT_BMS_CHARGE_RELAY, LOW);
	SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, LOW);
	SetDigitalOutputVal(ACT_INVERTER_RELAY, LOW);

	// Final Actuator: close FC_PWR (Power supply to controller & Fan)
	SetDigitalOutputVal(ACT_FC_POWER_RELAY, LOW);

	// Emergency systems
	SetDigitalOutputVal(ACT_EMERGENCY_VENT, HIGH);  // Emergency vent OPEN for safety
	SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, HIGH);  // Battery power ON for essential systems

	// Reset internal state
	_fcO2DryerActive = false;
	_tRunStart = 0;
	_state = EMERGENCY;
	DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "emergency_stop", "FuelCellController", "FC EMERGENCY STOP!");
}

void FuelCellController::handleFcO2Dryer() {
	// Integrated from FuelCellDryerController - Handle LS-04 dryer control
	unsigned long now = millis();

	// Fuel Cell O2 Dryer (LS-04)
	if (GetDigitalInputVal(PIN_FC_O2_DRYER_LEVEL_LS_04) == HIGH) {
		// Dryer level high (needs discharge)
		if (!_fcO2DryerActive
			&& (now - _lastFcO2DryerToggle > Constants::DRYER_MIN_TOGGLE_INTERVAL)) {

			_fcO2DryerActive = true;
			SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP_PM_03, HIGH);
			_lastFcO2DryerToggle = now;
			logMessage(LOG_INFO, F("FuelCellController: FC O2 dryer discharge started."));
		}
	}
	else {
		// Dryer level low (stop discharge)
		if (_fcO2DryerActive
			&& (now - _lastFcO2DryerToggle > Constants::DRYER_MIN_TOGGLE_INTERVAL)) {

			_fcO2DryerActive = false;
			SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP_PM_03, LOW);
			_lastFcO2DryerToggle = now;
			logMessage(LOG_INFO, F("FuelCellController: FC O2 dryer discharge stopped."));
		}
	}
}

const char* FuelCellController::stateToString(State s) const {
	switch (s) {
	case PRS4_POWER_CHECK: return "PRS4_GUC_KONTROL";
	case PRS4_GAS_CHECK: return "PRS4_GAZ_KONTROL";
	case PRS5_RUN: return "PRS5_CALISMA";
	case PRS6_SHUTDOWN: return "PRS6_KAPATMA";
	case STOPPED: return "FC_DURDURULDU";
	case EMERGENCY: return "FC_ACIL_DURDURMA";
	default: return "FC_BOS";
	}
}




