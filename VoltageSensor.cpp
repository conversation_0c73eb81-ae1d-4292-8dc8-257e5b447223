#include "VoltageSensor.h"
/* VoltageSensor.cpp */

VoltageSensor::VoltageSensor(uint8_t pin, float r1, float r2, float refVoltage) {
	_pin = pin;
	_r1 = r1;
	_r2 = r2;
	_refV = refVoltage;
	//pinMode(_pin, INPUT);
}

void VoltageSensor::calibrateZero(uint16_t samples) {
	// compute the divider ratio safely
	float ratio = (_r2 != 0.0f) ? ((_r1 + _r2) / _r2) : 0.0f;

	unsigned long sumRaw = 0;
	for (uint16_t i = 0; i < samples; i++) {
		sumRaw += analogRead(_pin);
		delay(10);
	}
	float avgRaw = sumRaw / float(samples);
	float vOut = (avgRaw * _refV) / 1023.0f;

	// use ratio instead of repeating the expression
	_offsetV = vOut * ratio;
}

float VoltageSensor::readVoltage() const {
	// compute the divider ratio safely
	float ratio = (_r2 != 0.0f) ? ((_r1 + _r2) / _r2) : 0.0f;

	int raw = analogRead(_pin);
	float vOut = (raw * _refV) / 1023.0f;
	float vin = vOut * ratio;

	return vin - _offsetV;
}