#ifndef CONSTANTS_H
#define CONSTANTS_H

/**
 * Constants.h
 * Contains all system-wide constants, enums, and pin definitions
 * for the Elektrolizor project.
 */

#include <Arduino.h>

 // Log levels for system messaging
enum LogLevel
{
	LOG_ERROR = 0,
	LOG_WARN,
	LOG_INFO,
	LOG_DEBUG
};
static const LogLevel currentLogLevel = LOG_INFO;

// Analog Sensor Indices
enum AnalogSensors
{
	//LS1
	//TT01
	//PT1
	//TT2
	//PT2
	//TT3
	//PT3
	//TT4

	//VFC
	//VBMS
	//VEL
	//CFC
	//CBMS
	//CEL
	//14

	ADC_SENSOR_WATER_LEVEL_LS_01 = 0,//LS-01: Main water tank level sensor (Ana su tankı seviye sensörü), ANALOG
	ADC_SENSOR_WATER_TEMP_TT_01,//TT01
	ADC_SENSOR_WATER_PRESS_PT_01,//PT01
	ADC_SENSOR_ELEC_TEMP_TT_02,//TT-02
	ADC_SENSOR_H2_TANK_PRESS_PT_02,//PT-02
	ADC_SENSOR_H2_TANK_TEMP_TT_03,//TT-03
	ADC_SENSOR_O2_TANK_PRESS_PT_03,//PT-03
	ADC_SENSOR_O2_TANK_TEMP_TT_04,//TT-04
	ADC_FC_VOLTAGE_VFC,// VFC
	ADC_SENSOR_BMS_VOLTAGE_VBMS,// VBMS
	ADC_SENSOR_ELEC_VOLTAGE_VEL,// VEL
	ADC_FC_CURRENT_CFC,// CFC
	ADC_SENSOR_BMS_CURRENT_CBMS, // CBMS
	ADC_SENSOR_ELEC_CURRENT_CEL// CEL
};

// Digital Actuator Pins
enum DigitalActuator
{
	ACT_FC_POWER_RELAY = 8,						// FC_PWR: Fuel Cell PSU Relay
	ACT_WATER_PUMP_PM_02 = 22,                    // PM-02: Water Pump
	ACT_WATER_INLET_VALVE_SV_01 = 23,             // SV-01: Water Inlet Valve
	ACT_EL_HEATER_EH_01 = 24,                     // EH-01: Electrolyzer Heater
	ACT_H2_OUTPUT_VALVE_SV_03 = 25,               // SV-03: H2 Output Valve
	ACT_O2_OUTPUT_VALVE_SV_02 = 26,               // SV-02: O2 Output Valve
	ACT_H2_DRYER_DISCHARGE_VALVE_SV_04 = 27,      // SV-04: H2 Dryer Discharge Valve
	ACT_EL_O2_CHILLER_CF_01 = 28,                 // CF-01: Electrolyzer O2 Chiller
	ACT_WATER_FILL_PUMP_PM_01 = 29,               // PM-01: Water Fill Pump
	ACT_FC_H2_SUPPLY_VALVE_SV_05 = 30,            // SV-05: Fuel Cell H2 Supply Valve
	ACT_FC_O2_SUPPLY_VALVE_SV_06 = 31,            // SV-06: Fuel Cell O2 Supply Valve
	ACT_FC_O2_FAN_RF_01 = 32,                     // RF-01: Fuel Cell O2 Fan
	ACT_FC_H2_DISCHARGE_VALVE_SV_07 = 33,         // SV-07: Fuel Cell H2 Discharge Valve
	ACT_FC_O2_CHILLER_CF_02 = 34,                 // CF-02: Fuel Cell O2 Chiller
	ACT_FC_O2_DRYER_PUMP_PM_03 = 35,              // PM-03: Fuel Cell O2 Dryer Pump
	ACT_FC_LOAD_RELAY = 36,                       // R_FC_LOAD: Fuel Cell Load Relay
	ACT_BMS_CHARGE_RELAY = 37,                    // R_BMS_CHARGE: BMS Charge Relay
	ACT_BMS_DISCHARGE_RELAY = 38,                 // R_BMS_DISCHARGE: BMS Discharge Relay
	ACT_INVERTER_RELAY = 39,                      // R_INVERTER: Inverter Relay
	ACT_EMERGENCY_VENT = 40,                      // Emergency Vent (Safety)
	ACT_MAIN_INPUT_RELAY = 41,                    // R_MAIN_INPUT: Main Input Relay (Safety)
	ACT_MAIN_BMS_OUT_RELAY = 42,                  // R_BMS_OUT: Main BMS Output Relay (Safety)
	ACT_EL_PSU_RELAY = 53,                        // Electrolyzer PSU Relay
	ACTUATOR_COUNT
};

// Actuator pin range constants
#define ACTUATOR_PIN_START 8
#define ACTUATOR_PIN_END 53  // One past the last actuator pin (42)
#define ACTUATOR_PIN_COUNT 23  // 22 actuators

 // List of power relay pins to invert logic
const uint8_t POWER_RELAY_PINS[] = {
  ACT_FC_POWER_RELAY, // 8
  ACT_WATER_FILL_PUMP_PM_01, // 29
  ACT_WATER_PUMP_PM_02, // 22
  ACT_MAIN_BMS_OUT_RELAY, // 42
  ACT_BMS_CHARGE_RELAY, // 37
  ACT_BMS_DISCHARGE_RELAY, // 38
  ACT_FC_LOAD_RELAY, // 36
  ACT_INVERTER_RELAY, // 39
  ACT_MAIN_INPUT_RELAY, // 41
  ACT_EMERGENCY_VENT, // 40
  ACT_EL_PSU_RELAY // 53
};

// Digital Input Pins
enum DigitalInput
{
	//PIN_ELEC_O2_DRYER_LEVEL_LS_02 = 47, // LS-02: Separator-01 level sensor (Separatör-01 seviye sensörü)
	//PIN_ELEC_H2_DRYER_LEVEL_LS_03 = 48, //LS-03: Waste water tank level sensor (Atık su tankı seviye sensörü)
	//PIN_FC_O2_DRYER_LEVEL_LS_04 = 49, //LS-04: Separator-03 level sensor (Separatör-03 seviye sensörü)
	PIN_SENSOR_FIRE_DETECT_FS_01 = 50, //FS-01, SAFETY
	PIN_SENSOR_H2_AMBIENT_HS_01 = 51,  //HS-01, SAFETY
	PIN_EMERGENCY_STOP_ES_01 = 52,     //ES-01 SAFETY
	DIGITAL_INPUT_COUNT
};

// Digital Input Pins
enum AnalogInput
{
	PIN_ELEC_O2_DRYER_LEVEL_LS_02 = A7, // LS-02: Separator-01 level sensor (Separatör-01 seviye sensörü)
	PIN_ELEC_H2_DRYER_LEVEL_LS_03 = A8, //LS-03: Waste water tank level sensor (Atık su tankı seviye sensörü)
	PIN_FC_O2_DRYER_LEVEL_LS_04 = A9,  //LS-04: Separator-03 level sensor (Separatör-03 seviye sensörü)
	ANALOG_INPUT_COUNT
};

// Sensor Calibration Indices
enum SensorCalIndex
{
	CAL_WATER_LEVEL = 0,
	CAL_WATER_TEMP,
	CAL_WATER_PRESS,
	CAL_ELEC_TEMP,
	CAL_H2_TANK_PRESS,
	CAL_H2_TANK_TEMP,
	CAL_O2_TANK_PRESS,
	CAL_O2_TANK_TEMP,
	CAL_FC_VOLTAGE,
	CAL_BMS_VOLTAGE,
	CAL_ELEC_VOLTAGE,
	CAL_FC_CURRENT,
	CAL_BMS_CURRENT,
	CAL_ELEC_CURRENT,
	CAL_SENSOR_COUNT
};

// Telemetry Command Types
enum TelemetryCommandType
{
	CMD_MODE_CHANGE_REQUEST = 0x01,
	CMD_CALIBRATION_GET = 0x02,
	CMD_CALIBRATION_UPDATE = 0x03,
	CMD_TELEMETRY_DATA = 0x04,
	CMD_ACKNOWLEDGMENT = 0x05,
	CMD_LOG_MESSAGE = 0x06,
	CMD_TELEMETRY_REQUEST = 0x07,
	CMD_SIMULATION_UPDATE = 0x08,
	CMD_SET_ACTUATOR = 0x09,
	CMD_SET_EL_PSU = 0x10,
	CMD_MESSAGE = 0x11,
	CMD_WARNING = 0x12
};

// ADS1115 Pins
#define ADS1_ALERT_PIN 43
#define ADS2_ALERT_PIN 44
#define ADS3_ALERT_PIN 45
#define ADS4_ALERT_PIN 46

// ELRegulator Pins
#define EL_REGULATOR_PWM_PIN 9        // PWM → PSU PWM input
#define EL_REGULATOR_VOLT_MON_PIN A2  // 0–5 V monitor (maps to 0–15 V)
#define EL_REGULATOR_CURR_MON_PIN A3  // 0–5 V monitor (maps to 0–55 A)

// ACS712 Current Sensor Pins
#define CURRENT_SENSOR_PIN_EL A5      // ACS712 current sensor for electrolyzer
#define CURRENT_SENSOR_PIN_FC A6      // ACS712 current sensor for fuel cell

// Voltage Sensor Pins
#define VOLTAGE_SENSOR_PIN_EL A7      // Voltage divider for electrolyzer voltage
#define VOLTAGE_SENSOR_PIN_FC A8      // Voltage divider for fuel cell voltage

// ADS1115 Settings
#define ADS_GAIN GAIN_TWOTHIRDS //GAIN_ONE
// #define ADS_SPS RATE_ADS1115_128SPS
// #define ADS_SPS RATE_ADS1115_250SPS
#define ADS_SPS RATE_ADS1115_860SPS
#define ADS_FULL_SCALE 6.144

// PZEM Settings
#define NUM_PZEMS 3

// #if !defined(PZEM_RX_PIN)
#define PZEM_RX_PIN 16
// #endif

// #if !defined(PZEM_TX_PIN)
#define PZEM_TX_PIN 17
// #endif

#if !defined(PZEM_SERIAL)
#define PZEM_SERIAL Serial2
#endif

// SERIAL SETTINGS
//  Aliases for clarity
#if !defined(DEBUG_SERIAL)
#define DEBUG_SERIAL Serial // USB serial for debug/logging, TEST
// #define DEBUG_SERIAL Serial1 // USB serial for debug/logging
#endif

#if !defined(TELEMETRY_SERIAL)
// #define TELEMETRY_SERIAL Serial1 // Hardware serial 1 for telemetry, TEST
#define TELEMETRY_SERIAL Serial // Hardware serial 1 for telemetry
#endif

// IsI2CSensorsOn
#define IsI2CSensorsOn true

// Simulation Mode
#define SIM_MODE_INITIAL true
#define NUM_ANALOG_SENSORS 14
#define SIM_UPDATE_PAYLOAD_LENGTH 205

// Predictive Buffer Size
#define PREDICTIVE_SAMPLES 30

/**
 * System Constants - Operational parameters for the system
 * Includes timing, safety thresholds, and operational limits
 */
namespace Constants
{
	// Timing constants (milliseconds)
	const unsigned long CONTROL_LOOP_FREQ_MS = 100;   // Main control loop frequency
	const unsigned long BMSCONTROL_LOOP_FREQ_MS = 30000;   // BMS control loop frequency
	const unsigned long TELEMETRY_INTERVAL_MS = 400; // Telemetry update interval
	const unsigned long MODE_CHANGE_DELAY_MS = 5000;  // Delay for mode transitions
	const unsigned long MAIN_LOOP_WATCHDOG_MS = 2000; // Watchdog timeout

	// Mode transition timeout multiplier
	const unsigned int MODE_TRANSITION_TIMEOUT_MULTIPLIER = 2; // Multiplier for calculating transition timeout

	// Controller timing constants (Updated per REDeS procedures)
	const unsigned long ELECTROLYZER_MIN_SWITCH_INTERVAL = 5000; // 5 seconds minimum between electrolyzer component switches
	const unsigned long DRYER_MIN_TOGGLE_INTERVAL = 10000;       // 10 seconds minimum between dryer toggles
	const unsigned long ELECTROLYZER_SHUTDOWN_DELAY = 5000;      // 5 seconds delay before shutdown (PRS-3)
	const unsigned long HEATER_MAX_RUNTIME = 60000;              // 1 minute max heater runtime (PRS-2)
	//commented out and now controlled by level switch
	//const unsigned long H2_DRYER_DISCHARGE_TIME = 20000;         // 20 seconds H2 dryer discharge time (LS-03), 
	const unsigned long FC_H2_DISCHARGE_TIME = 10000;            // 10 seconds FC H2 discharge time (PRS-6)

	// Control flags
	const bool IsHeaterEnabled = false;                                 // Global flag to enable/disable heater operation
	const bool IsVoltageCheckEnabled = false;                           // Global flag to enable/disable electrolyzer voltage checks
	const bool IsPSUChecksEnabled = false;                              // Global flag to enable/disable 24V/12V power supply checks

	// SystemController mode transition timing constants
	const unsigned long ELECTROLYZER_ACTIVATION_DELAY_MS = 500;  // Delay before activating heater in electrolyzer mode
	const unsigned long FUEL_CELL_ACTIVATION_DELAY_MS = 1000;    // Delay before activating load relay in fuel cell mode
	const unsigned long BATTERY_INVERTER_ACTIVATION_DELAY_MS = 2000; // Delay before activating inverter in battery mode
	const unsigned long SYSTEM_TEST_PIN_TOGGLE_INTERVAL_MS = 200;    // Interval for toggling pins during system test

	// SafetyController timing constants
	const unsigned long H2_LEAK_CLEAR_TIMEOUT_MS = 10000;       // Time to wait before clearing H2 leak fault
	const unsigned long COMM_WATCHDOG_TIMEOUT_MS = 5000;        // Communication timeout threshold

	// Main loop timing constants
	const unsigned long WATCHDOG_RESET_INTERVAL_MS = 1000;      // Watchdog timer reset interval

	// Safety thresholds (Updated per REDeS working procedures)
	// NOTE: Pressure sensors have 1 bar offset (read 0 at 1 bar actual pressure)
	const float H2_LEAK_THRESHOLD_PERCENT = 4.0f; // H2 leak detection threshold
	const float MAX_OPERATING_TEMP_C = 65.0f;     // Maximum safe operating temperature (15-55°C per spec)
	const float MAX_TANK_PRESSURE_BAR = 4.0f;     // Maximum tank pressure (4 barg with sensor offset)
	const float MIN_TANK_PRESSURE_BAR = 0.5f;     // Minimum tank pressure for fuel cell operation (0.5 barg with sensor offset)

	// Electrolyzer operational constants (Updated per PRS-1, PRS-2)
	const float WATER_PRESSURE_START = 3.5f;      // Water pressure to start pump (bar)
	const float WATER_PRESSURE_STOP = 4.0f;       // Water pressure to stop pump (4 barg with sensor offset)
	const float WATER_LEVEL_MIN = 10.0f;           // Minimum water level (%)
	const float WATER_TEMP_LOW = 15.0f;           // Low water temperature threshold (15°C per spec)
	const float WATER_TEMP_TARGET = 40.0f;        // Target water temperature (°C)
	const float WATER_TEMP_HIGH = 55.0f;          // High water temperature threshold (55°C per spec)
	const float ELEC_TEMP_HIGH = 55.0f;           // High electrolyzer temperature threshold (55°C per spec)
	const float ELEC_TEMP_LOW = 15.0f;            // Low electrolyzer temperature threshold (15°C per spec)
	const float PRODUCTION_START_TEMP = 20.0f;    // Temperature to start production (20°C per spec)
	const float PRODUCTION_STOP_TEMP = 15.0f;     // Temperature to stop production (15°C per spec)

	// Temperature control hysteresis constants
	//const float TEMP_HYSTERESIS_OFFSET = 5.0f;    // Temperature offset for hysteresis control (°C)

	// Fuel cell operational constants (Updated per PRS-4, PRS-5, PRS-6)
	const float MIN_OPERATING_VOLTAGE = 1.0f;     // Minimum fuel cell operating voltage (V)
	const float MAX_OPERATING_VOLTAGE = 15.5f;    // Maximum fuel cell operating voltage (V)
	const float MIN_OUTPUT_POWER = 1.0f;          // Minimum fuel cell output power (W)
	const float MAX_OUTPUT_POWER = 110.0f;        // Maximum fuel cell output power (W)
	const float OUTPUT_POWER_SETPOINT = 100.0f;   // Fuel cell power setpoint (W)
	const float FC_H2_SHUTDOWN_PRESSURE = 0.5f;   // H2 pressure to shutdown fuel cell (0.5 barg with sensor offset)
	const float FC_H2_WARNING_PRESSURE = 1.0f;    // H2 pressure warning threshold (1 barg with sensor offset)
	const float FC_GAS_TEMP_MIN = 15.0f;          // Minimum gas temperature (15°C per spec)
	const float FC_GAS_TEMP_MAX = 65.0f;          // Maximum gas temperature (65°C per spec)

	// ELRegulator constants
	const float EL_REGULATOR_SUPPLY_MAX_V = 15.0f; // full-scale voltage
	const float EL_REGULATOR_SUPPLY_MIN_V = 10.0f; // min voltage
	const float EL_REGULATOR_SUPPLY_MAX_I = 55.0f; // full-scale current
	const float EL_REGULATOR_ADC_REF = 5.0f;       // Arduino analog reference (DEFAULT = Vcc)
	const int EL_REGULATOR_ADC_MAX = 1023;         // 10-bit max

	// Battery parameters
	const float BATT_MAX_VOLTAGE_V = 29.2f;      // Maximum battery voltage
	const float BATT_MIN_VOLTAGE_V = 20.0f;      // Minimum battery voltage
	//const float BATT_MAX_TEMP_C = 45.0f;         // Maximum battery temperature
	const float LOW_BATTERY_THRESHOLD = 21.0f;   // Low battery warning threshold
	const float CHARGE_OFF_THRESHOLD = 28.0f;    // Stop charging threshold
	const float DISCHARGE_ON_THRESHOLD = 28.0f;  // Start discharge threshold
	const float DISCHARGE_OFF_THRESHOLD = 20.0f; // Stop discharge threshold
}

// Calibration Constants
static const uint32_t CALIBRATION_MAGIC_VALUE = 0xB1ADBE1B;
static const uint32_t CALIBRATION_VERSION = 0x10000010;


#endif // CONSTANTS_H
