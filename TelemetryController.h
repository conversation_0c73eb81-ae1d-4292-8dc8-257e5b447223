#ifndef TELEMETRY_CONTROLLER_H
#define TELEMETRY_CONTROLLER_H

/**
 * TelemetryController.h
 * Handles communication with external systems
 * Processes commands and sends system state information
 */

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"


struct ActuatorAckData {
	uint8_t success;
	uint8_t pin;
	uint8_t commandId;
	uint8_t timestamp;
};

/**
 * Control command structure
 * Used for parsing incoming commands from serial interface
 */
struct ControlCommand {
	uint8_t command;
	uint8_t dataLength;
	uint8_t data[128];
};

/**
 * TelemetryController class
 * Manages communication protocol, command processing, and telemetry
 */
class TelemetryController {
private:
	// Packet reception state machine
	enum RxState { WAIT_HEADER1, WAIT_HEADER2, WAIT_LENGTH, WAIT_PAYLOAD };
	RxState rxState;
	uint8_t rxIndex;
	uint8_t expectedPayloadLength;
	uint8_t incomingDataLength;
	uint8_t rxBuffer[256];

	// Private methods for packet processing
	void processByte(Stream& serialport, uint8_t b);
	void processCommand(Stream& serialport, const ControlCommand& cmd);
public:
	TelemetryController();

	/**
	 * Process any incoming commands from serial interface
	 * @param serialport The serial interface to read from
	 */
	void processIncomingCommands(Stream& serialport);

	/**
	 * Send telemetry data packet with current system state
	 * @param serialport The serial interface to send to
	 */
	void sendTelemetry(Stream& serialport);

	/**
	 * Send acknowledgment for a received command
	 * @param serialport The serial interface to send to
	 * @param origCommand The original command being acknowledged
	 * @param success Whether the command was successful
	 * @param message Text message to include in acknowledgment
	 */
	void sendAck(Stream& serialport, uint8_t origCommand, bool success, const char* message,
		const ActuatorAckData* actuatorData = nullptr);

	/**
	 * Send a general message to the external system
	 * @param serialport The serial interface to send to
	 * @param message Text message to send
	 */
	void sendMessage(Stream& serialport, const char* message);

	/**
	 * Send a general message with command context to the external system
	 * @param serialport The serial interface to send to
	 * @param commandId The command enum ID that triggered this message
	 * @param methodName The name of the method sending the message
	 * @param controllerName The name of the controller sending the message
	 * @param message Text message to send
	 */
	void SendMessage(Stream& serialport, uint8_t commandId, const char* methodName, const char* controllerName, const char* message);

	/**
	 * Send a warning message with command context to the external system
	 * @param serialport The serial interface to send to
	 * @param commandId The command enum ID that triggered this warning
	 * @param methodName The name of the method sending the warning
	 * @param controllerName The name of the controller sending the warning
	 * @param message Warning message text
	 */
	void showWarning(Stream& serialport, uint8_t commandId, const char* methodName, const char* controllerName, const char* message);

	/**
	 * Send a log message to the external system
	 * @param serialport The serial interface to send to
	 * @param level Log level (error, warning, info, debug)
	 * @param message The log message text
	 */
	void sendLogMessage(Stream& serialport, LogLevel level, const char* message);

	/**
	 * Send a raw packet with the communication protocol framing
	 * @param serialport The serial interface to send to
	 * @param command Command code
	 * @param data Payload data
	 * @param dataLength Length of payload data
	 */
	void sendPacket(Stream& serialport, uint8_t command, const uint8_t* data, uint8_t dataLength);

};

extern TelemetryController telemetry_controller;

#endif // TELEMETRY_CONTROLLER_H
