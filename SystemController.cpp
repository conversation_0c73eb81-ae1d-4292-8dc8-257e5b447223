#include "SystemController.h"
#include "ElectrolyzerController.h"
#include "FuelCellController.h"
#include "ELRegulator.h"
#include "SafetyController.h"

// Global instance of the fuel-cell controller:
extern FuelCellController fuel_cell_controller;
extern ElectrolyzerController electrolyzer_controller;
extern ELRegulator elRegulator;
extern SafetyController safety_controller;

SystemController system_controller;

// ——— Constructor ———
SystemController::SystemController()
	: currentMode(MODE_SAFE),
	pendingMode(MODE_SAFE),
	inTransition(false),
	transitionStartTs(0),
	subState(TRANS_IDLE),
	stepStartTs(0)
{
}

// ——— Public API ———
void SystemController::request_mode(OperationMode m)
{
	if (currentMode == MODE_EMERGENCY)
	{
		logMessage(LOG_WARN, F("Already in EMERGENCY, ignoring request "));
		return;
	}
	if (m == currentMode)
	{
		logMessage(LOG_INFO, F("Already in mode"), getModeName(m));
		return;
	}
	if (!validate_transition(m))
	{
		// Build a single String for the “not allowed” suffix:
		String msg = String(getModeName(m)) + " not allowed";
		logMessage(LOG_ERROR, F("Transition to"), msg);
		return;
	}
	begin_transition(m);
}

void SystemController::update()
{
	if (inTransition)
	{
		unsigned long now = millis();
		if (now - transitionStartTs >
			Constants::MODE_CHANGE_DELAY_MS * Constants::MODE_TRANSITION_TIMEOUT_MULTIPLIER)
		{
			logMessage(LOG_ERROR, F("Mode change timeout, reverting to SAFE"));
			force_mode(MODE_SAFE);
			return;
		}
		activate_pending_mode();
	}
}

void SystemController::force_mode(OperationMode m)
{
	String msg = String(getModeName(m));
	logMessage(LOG_WARN, F("Force mode =>"), msg);
	deactivate_current_mode();
	currentMode = m;
	pendingMode = m;
	inTransition = false;
	subState = TRANS_DONE;
	if (m == MODE_EMERGENCY)
	{
		SetDigitalOutputVal(ACT_EMERGENCY_VENT, HIGH);
	}
}

// ——— Helpers ———
bool SystemController::validate_transition(OperationMode m)
{
	if (m == MODE_EMERGENCY || m == MODE_SAFE)
		return true;

	float h2p = getFilteredCalibratedValueADS(
		ADC_SENSOR_H2_TANK_PRESS_PT_02, CAL_H2_TANK_PRESS);
	float o2p = getFilteredCalibratedValueADS(
		ADC_SENSOR_O2_TANK_PRESS_PT_03, CAL_O2_TANK_PRESS);
	if (h2p > Constants::MAX_TANK_PRESSURE_BAR ||
		o2p > Constants::MAX_TANK_PRESSURE_BAR)
	{
		logMessage(LOG_ERROR, F("Cannot switch, tank overpressure"));
		return false;
	}
	if (m == MODE_ELECTROLYZER)
	{
		//TODO: sensör değişince
		/*float lvl = getFilteredCalibratedValueADS(
			ADC_SENSOR_WATER_LEVEL_LS_01, CAL_WATER_LEVEL);
		if (lvl < Constants::WATER_LEVEL_MIN)
		{
			logMessage(LOG_ERROR, F("Cannot start electrolyzer, LS-01 water level low"));
			return false;
		}*/
		uint8_t lvl02 = GetLevelSwitchInputVal(PIN_ELEC_O2_DRYER_LEVEL_LS_02);
		if (lvl02 < Constants::WATER_LEVEL_MIN)
		{
			logMessage(LOG_ERROR, F("Cannot start electrolyzer, LS-02 water level low"));
			return false;
		}
	}
	else if (m == MODE_FUEL_CELL)
	{
		// Validate fuel cell requirements: sufficient gas pressure
		if (h2p <= Constants::MIN_TANK_PRESSURE_BAR || o2p <= Constants::MIN_TANK_PRESSURE_BAR)
		{
			logMessage(LOG_ERROR, F("Cannot start fuel cell, insufficient gas pressure"));
			return false;
		}
	}
	else if (m == MODE_BATTERY_INVERTER)
	{
		// Validate battery requirements: check BMS voltage
		float battVolt = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_VOLTAGE_VBMS, CAL_BMS_VOLTAGE);
		if (battVolt < Constants::LOW_BATTERY_THRESHOLD)
		{
			logMessage(LOG_ERROR, F("Cannot start battery inverter, battery voltage too low"));
			return false;
		}
	}
	return true;
}

void SystemController::begin_transition(OperationMode m)
{
	// Build an arrow string:
	String arrow = String(getModeName(currentMode)) + " -> " + getModeName(m);
	logMessage(LOG_INFO, F("Transition start"), arrow);

	deactivate_current_mode();
	pendingMode = m;
	inTransition = true;
	subState = TRANS_IDLE;
	transitionStartTs = millis();
}

void SystemController::deactivate_current_mode()
{
	logMessage(LOG_INFO, F("Deactivating mode: "), getModeName(currentMode));

	switch (currentMode)
	{
	case MODE_ELECTROLYZER:
		electrolyzer_controller.emergency_stop();
		SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);
		break;
	case MODE_FUEL_CELL:
		fuel_cell_controller.deactivate();
		SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, LOW);  // Turn off BMS power
		break;
	case MODE_BATTERY_INVERTER:
		// Proper shutdown sequence: inverter first, then discharge, then BMS
		SetDigitalOutputVal(ACT_INVERTER_RELAY, LOW);
		delay(100);  // Allow inverter to shut down
		SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, LOW);
		delay(100);  // Allow discharge to stop
		SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, LOW);
		break;
	default:
		break;
	}

	// Always reset electrolyzer regulator and wait for stabilization
	elRegulator.setOutputVoltage(0.0f);
	delay(200);  // Allow system to stabilize before next mode activation
}

void SystemController::activate_pending_mode()
{

	logMessage(LOG_ERROR, F("SystemController activate_pending_mode"));
	switch (pendingMode)
	{
	case MODE_ELECTROLYZER:
		activateElectrolyzer();
		break;
	case MODE_FUEL_CELL:
		activateFuelCell();
		break;
	case MODE_BATTERY_INVERTER:
		activateBatteryInverter();
		break;
	case MODE_SYSTEM_TEST:
		activateSystemTest();
		break;
	case MODE_SAFE:
		force_mode(MODE_SAFE);
		break;
	case MODE_EMERGENCY:
		force_mode(MODE_EMERGENCY);
		break;
	default:
		break;
	}
}

void SystemController::complete_transition()
{
	currentMode = pendingMode;
	inTransition = false;
	subState = TRANS_DONE;
	logMessage(LOG_INFO, F("Mode active =>"), getModeName(currentMode));
}

bool IsElectrolyzerStarted = false;
unsigned long electrolyzertransitionStartTime = 0;
// ——— Mode-specific activations ———
void SystemController::activateElectrolyzer()
{
	unsigned long now = millis();

	switch (subState)
	{
	case TRANS_IDLE:
		if (!IsElectrolyzerStarted)
		{
			logMessage(LOG_INFO, F("Activating ELECTROLYZER"));
			SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, HIGH);
			electrolyzer_controller.reset();
			subState = TRANS_VERIFY;
			IsElectrolyzerStarted = true;
			electrolyzertransitionStartTime = now;
		}
		break;
	case TRANS_VERIFY:
		// Check if electrolyzer reached PRS2_RUN state
		if (electrolyzer_controller.getState() == ElectrolyzerController::PRS2_RUN)
		{
			complete_transition();
			IsElectrolyzerStarted = false;
			logMessage(LOG_INFO, F("Electrolyzer successfully activated and running"));
		}
		// Check for timeout to prevent infinite reset loop
		else if (now - electrolyzertransitionStartTime > 30000) // 30 second timeout
		{
			logMessage(LOG_ERROR, F("Electrolyzer activation timeout - reverting to SAFE mode"));
			logMessage(LOG_ERROR, F("Current electrolyzer state: "), String(electrolyzer_controller.getState()));
			force_mode(MODE_SAFE);
			IsElectrolyzerStarted = false;
		}
		// Log current state for debugging
		else if ((now - electrolyzertransitionStartTime) % 5000 == 0) // Every 5 seconds
		{
			logMessage(LOG_INFO, F("Waiting for electrolyzer activation, current state: "), String(electrolyzer_controller.getState()));
		}
		break;
	default:
		break;
	}
}

void SystemController::activateFuelCell()
{
	switch (subState)
	{
	case TRANS_IDLE:
		logMessage(LOG_INFO, F("Activating FUEL_CELL"));
		// Fuel cell uses BMS battery power, not AC power
		SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);   // Turn off AC power

		// Check BMS status before enabling battery power
		float battVolt = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_VOLTAGE_VBMS, CAL_BMS_VOLTAGE);
		if (battVolt >= Constants::LOW_BATTERY_THRESHOLD) {
			SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, HIGH); // Turn on BMS battery power
			fuel_cell_controller.reset();
			subState = TRANS_VERIFY;
		}
		else {
			logMessage(LOG_ERROR, F("Cannot activate fuel cell: battery voltage too low"));
			force_mode(MODE_SAFE);
		}
		break;
	case TRANS_VERIFY:
		if (fuel_cell_controller.getState() == FuelCellController::PRS5_RUN)
		{
			complete_transition();
		}
		else if (millis() - transitionStartTs > 15000) {
			// Timeout after 15 seconds if fuel cell doesn't reach PRS5_RUN
			logMessage(LOG_ERROR, F("Fuel cell activation timeout - failed to reach PRS5_RUN"));
			force_mode(MODE_SAFE);
		}
		break;
	default:
		break;
	}
}

void SystemController::activateBatteryInverter()
{
	switch (subState)
	{
	case TRANS_IDLE:
		logMessage(LOG_INFO, F("Activating BATTERY_INVERTER"));

		// Check BMS status before enabling battery power
		float battVolt = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_VOLTAGE_VBMS, CAL_BMS_VOLTAGE);
		if (battVolt >= Constants::LOW_BATTERY_THRESHOLD) {
			// Battery inverter mode uses battery power, not AC power
			SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);        // Turn off AC power

			// Power sequencing: BMS first, then discharge, then inverter
			SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, HIGH);     // Turn on BMS battery power
			delay(100);  // Allow BMS to stabilize
			SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, HIGH);    // Enable battery discharge
			delay(100);  // Allow discharge to stabilize

			//Todo:Check relay
			SetDigitalOutputVal(ACT_INVERTER_RELAY, HIGH);         // Turn on inverter

			fuel_cell_controller.reset();
			subState = TRANS_VERIFY;
		}
		else {
			logMessage(LOG_ERROR, F("Cannot activate Battery Inverter: battery voltage too low"));
			force_mode(MODE_SAFE);
		}

		subState = TRANS_VERIFY;
		break;
	case TRANS_VERIFY:
	// Verify battery inverter is working properly
	{
		float battVolt = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_VOLTAGE_VBMS, CAL_BMS_VOLTAGE);
		float battCurrent = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_CURRENT_CBMS, CAL_BMS_CURRENT);

		// Check if battery is discharging and voltage is stable
		if (battVolt >= Constants::DISCHARGE_OFF_THRESHOLD && battCurrent > 0.1f) {
			complete_transition();
		}
		else if (millis() - transitionStartTs > 5000) {
			// Timeout after 5 seconds if verification fails
			logMessage(LOG_ERROR, F("Battery inverter verification failed"));
			force_mode(MODE_SAFE);
		}
	}
	break;
	default:
		break;
	}
}

void SystemController::activateSystemTest()
{
	switch (subState)
	{
	case TRANS_IDLE:
		logMessage(LOG_INFO, F("Activating SYSTEM_TEST"));
		SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, HIGH);
		subState = TRANS_VERIFY;
		break;
	case TRANS_VERIFY:
		// Simple verification - just complete transition
		complete_transition();
		break;
	default:
		break;
	}
}

const __FlashStringHelper* SystemController::getModeName(OperationMode m)
{
	switch (m)
	{
	case MODE_SAFE:
		return F("SAFE");
	case MODE_ELECTROLYZER:
		return F("ELECTROLYZER");
	case MODE_FUEL_CELL:
		return F("FUEL_CELL");
	case MODE_BATTERY_INVERTER:
		return F("BATTERY_INVERTER");
	case MODE_SYSTEM_TEST:
		return F("SYSTEM_TEST");
	case MODE_EMERGENCY:
		return F("EMERGENCY");
	default:
		return F("UNKNOWN");
	}
}