#include "Simulation.h"

// Initialize simulation constants
namespace SimulationValues {
	// Final calibrated simulation values - these are the actual values we want to see
	// In simulation mode, these bypass the calibration and are returned directly

	const float WATER_LEVEL_RAW = 50.0f;        // → 50% (Water Level)
	const float WATER_TEMP_RAW = 30.0f;         // → 30°C (Water Temp) - Safe operating temp
	const float WATER_PRESS_RAW = 3.0f;         // → 3 bar (Water Pressure)
	const float ELEC_VOLTAGE = 0.0f;            // → 0V (Electrolyzer Voltage)
	const float ELEC_CURRENT_RAW = 0.0f;        // → 0A (Electrolyzer Current)
	const float ELECTROLIZOR_TEMP_RAW = 35.0f;  // → 35°C (Electrolyzer Temp) - Safe operating temp
	const float ELECTROLIZOR_PRESS_RAW = 3.0f;  // → 3 bar (Electrolyzer Pressure)
	const float H2_TANK_TEMP_RAW = 30.0f;       // → 30°C (H₂ Tank Temp) - Safe operating temp
	const float H2_TANK_PRESS_RAW = 2.0f;       // → 2 bar (H₂ Tank Pressure) - Safe for startup
	const float O2_TANK_TEMP_RAW = 30.0f;       // → 30°C (O₂ Tank Temp) - Safe operating temp
	const float O2_TANK_PRESS_RAW = 2.0f;       // → 2 bar (O₂ Tank Pressure) - Safe for startup
	const float FC_VOLTAGE = 0.0f;              // → 0V (Fuel Cell Voltage)
	const float FC_CURRENT_RAW = 0.0f;          // → 0A (Fuel Cell Current)
	const float BMS_VOLTAGE_RAW = 24.0f;        // → 24V (BMS Voltage)
	const float BMS_CURRENT_RAW = 0.0f;         // → 0A (BMS Current)
}

// Global simulation variables
bool simulationMode = SIM_MODE_INITIAL;
float analogSensors[NUM_ANALOG_SENSORS];
float adsSensors[14];
PZEMModel pzemModel[NUM_PZEMS];
DigitalInputSimulation simDigitalInputs;
AnalogInputSimulation simAnalogInputs;
bool simDigitalOutputs[ACTUATOR_PIN_COUNT];

float currentACS_BMS_SimValue = 5.0f;  // BMS OUTPUT current, sim value (A)
float currentACS_EL_SimValue = 10.0f; // Electrolyzer input current, sim value (A)
float currentACS_FC_SimValue = 3.0f;  // Fuel Cell output current, sim value (A)
float volt_EL_SimValue = 12.0f;       // Electrolyzer input Volt, sim value (V)
float volt_BMS_SimValue = 24.5f;      // BMS Volt, sim value (V)
float volt_FC_SimValue = 12.0f;       // Fuel Cell output Volt, sim value (V)

// Initialize simulation values
void initializeSimulation() {
	// Initialize analog sensors with default values
	for (int i = 0; i < NUM_ANALOG_SENSORS; i++) {
		analogSensors[i] = 0.0f;
	}

	// Set specific analog sensor values (reordered to match new AnalogSensors enum)
	analogSensors[ADC_SENSOR_WATER_LEVEL_LS_01] = SimulationValues::WATER_LEVEL_RAW;
	analogSensors[ADC_SENSOR_WATER_TEMP_TT_01] = SimulationValues::WATER_TEMP_RAW;
	analogSensors[ADC_SENSOR_WATER_PRESS_PT_01] = SimulationValues::WATER_PRESS_RAW;
	analogSensors[ADC_SENSOR_ELEC_TEMP_TT_02] = SimulationValues::ELECTROLIZOR_TEMP_RAW;
	analogSensors[ADC_SENSOR_H2_TANK_PRESS_PT_02] = SimulationValues::H2_TANK_PRESS_RAW;
	analogSensors[ADC_SENSOR_H2_TANK_TEMP_TT_03] = SimulationValues::H2_TANK_TEMP_RAW;
	analogSensors[ADC_SENSOR_O2_TANK_PRESS_PT_03] = SimulationValues::O2_TANK_PRESS_RAW;
	analogSensors[ADC_SENSOR_O2_TANK_TEMP_TT_04] = SimulationValues::O2_TANK_TEMP_RAW;
	analogSensors[ADC_FC_VOLTAGE_VFC] = SimulationValues::FC_VOLTAGE;
	analogSensors[ADC_SENSOR_BMS_VOLTAGE_VBMS] = SimulationValues::BMS_VOLTAGE_RAW;
	analogSensors[ADC_SENSOR_ELEC_VOLTAGE_VEL] = SimulationValues::ELEC_VOLTAGE;
	analogSensors[ADC_FC_CURRENT_CFC] = SimulationValues::FC_CURRENT_RAW;
	analogSensors[ADC_SENSOR_BMS_CURRENT_CBMS] = SimulationValues::BMS_CURRENT_RAW;
	analogSensors[ADC_SENSOR_ELEC_CURRENT_CEL] = SimulationValues::ELEC_CURRENT_RAW;

	// Copy the same values to ADS sensors
	for (int i = 0; i < 14; i++) {
		adsSensors[i] = (i < NUM_ANALOG_SENSORS) ? analogSensors[i] : 0.0f;
	}

	// Debug print simulation values
	Serial.println(F("=== SIMULATION VALUES INITIALIZED ==="));
	Serial.print(F("Water Temp Raw: ")); Serial.println(SimulationValues::WATER_TEMP_RAW, 3);
	Serial.print(F("Elec Temp Raw: ")); Serial.println(SimulationValues::ELECTROLIZOR_TEMP_RAW, 3);
	Serial.print(F("H2 Tank Temp Raw: ")); Serial.println(SimulationValues::H2_TANK_TEMP_RAW, 3);
	Serial.print(F("O2 Tank Temp Raw: ")); Serial.println(SimulationValues::O2_TANK_TEMP_RAW, 3);
	Serial.print(F("Water Press Raw: ")); Serial.println(SimulationValues::WATER_PRESS_RAW, 3);
	Serial.print(F("H2 Tank Press Raw: ")); Serial.println(SimulationValues::H2_TANK_PRESS_RAW, 3);
	Serial.print(F("O2 Tank Press Raw: ")); Serial.println(SimulationValues::O2_TANK_PRESS_RAW, 3);
	Serial.print(F("Water Level Raw: ")); Serial.println(SimulationValues::WATER_LEVEL_RAW, 3);
	Serial.println(F("=== END SIMULATION VALUES ==="));

	// Initialize PZEM models
	for (int i = 0; i < NUM_PZEMS; i++) {
		pzemModel[i].voltage = 0.0f;
		pzemModel[i].current = 0.0f;
		pzemModel[i].power = 0.0f;
		pzemModel[i].energy = 0.0f;
		pzemModel[i].frequency = 50.0f;
		pzemModel[i].pf = 1.0f;
	}

	// Initialize analog level switch inputs (LS-02, LS-03, LS-04)
	simAnalogInputs.elecO2DryerLevel = 50;   // Normal level (not full)
	simAnalogInputs.elecH2DryerLevel = 0;    // Empty level (normal operation)
	simAnalogInputs.fcO2DryerLevel = 50;     // Normal level (not full)

	// Initialize digital inputs (safety sensors)
	// NOTE: These sensors are active LOW, so HIGH = normal, LOW = alarm
	simDigitalInputs.sensorFireDetect = true;     // HIGH = No fire detected (normal)
	simDigitalInputs.sensorH2Ambient = true;      // HIGH = No H2 leak detected (normal)
	simDigitalInputs.buttonEmergencyStop = false; // Emergency stop not pressed

	// Initialize digital outputs (all actuators off)
	for (int i = 0; i < ACTUATOR_PIN_COUNT; i++) {
		simDigitalOutputs[i] = false;
	}
}
