#include <Arduino.h>
#include "Utilities.h"
#include "TelemetryController.h"
#include <CRC.h>
#include <jm_crc-ccitt.h>
#include "Constants.h"
#include <HardwareSerial.h>

// #define LOG_SERIAL Serial1      // USB serial for debug/logging

// Logging functions
void logMessage(LogLevel level, const __FlashStringHelper* msg) {
	//if (level <= currentLogLevel) {
	  // switch(level) {
	  //   case LOG_ERROR: Serial.println(F("[ERROR] ")); break;
	  //   case LOG_WARN: Serial.println(F("[WARN] ")); break;
	  //   case LOG_INFO: Serial.println(F("[INFO] ")); break;
	  //   case LOG_DEBUG: Serial.println(F("[DEBUG] ")); break;
	  // }
	String s = String(msg);
	//Serial1: DEBUG_SERIAL
	telemetry_controller.sendLogMessage(DEBUG_SERIAL, level, s.c_str());
	//}
}

void logMessage(LogLevel level, const __FlashStringHelper* msg, const String& val) {
	//if (level <= currentLogLevel) {
	  // switch(level) {
	  //   case LOG_ERROR: Serial.println(F("[ERROR] ")); break;
	  //   case LOG_WARN: Serial.println(F("[WARN] ")); break;
	  //   case LOG_INFO: Serial.println(F("[INFO] ")); break;
	  //   case LOG_DEBUG: Serial.println(F("[DEBUG] ")); break;
	  // }
	String s = String(msg) + " " + val;
	telemetry_controller.sendLogMessage(DEBUG_SERIAL, level, s.c_str());
	//}
}

void logMessage(const __FlashStringHelper* message) {
	String s(message);
	telemetry_controller.sendLogMessage(DEBUG_SERIAL, LOG_INFO, s.c_str());
}

// CRC calculation
uint16_t calculateCRC(const uint8_t* data, uint8_t len) {
	uint16_t crc = 0xFFFF;
	for (uint8_t i = 0; i < len; i++) {
		crc = _crc_ccitt_update(crc, data[i]);
	}
	return crc;
}
