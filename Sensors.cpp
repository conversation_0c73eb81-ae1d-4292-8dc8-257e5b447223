#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include <EEPROM.h>
#include <string.h>
#include <math.h>
#include "VoltageSensor.h"
#include "ACS712Meter.h"
#include "ELRegulator.h"
#include "Simulation.h"
#include "Utilities.h"
#include <HardwareSerial.h>

// Default calibration data
const CalibrationData default_calibration = {
	CALIBRATION_VERSION,
	{
		// {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f},
		// {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f},
		// {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f}, {0.0f, 1.0f}

		{0.0f, 16.0f},  // ADC_SENSOR_WATER_LEVEL_LS_01
		{0.0f, 16.0f},  // ADC_SENSOR_WATER_TEMP_TT_01
		{0.0f, 2.0f},   // ADC_SENSOR_WATER_PRESS_PT_01
		{0.0f, 16.0f},  // ADC_SENSOR_ELEC_TEMP_TT_02

		{0.0f, 2.0f},   // ADC_SENSOR_H2_TANK_PRESS_PT_02
		{0.0f, 16.0f},  // ADC_SENSOR_H2_TANK_TEMP_TT_03
		{0.0f, 2.0f},   // ADC_SENSOR_O2_TANK_PRESS_PT_03
		{0.0f, 16.0f},  // ADC_SENSOR_O2_TANK_TEMP_TT_04

		{0.0f, 3.5f},   // ADC_FC_VOLTAGE_VFC
		{0.0f, 1.0f},   // ADC_SENSOR_BMS_VOLTAGE_VBMS
		{0.0f, 4.0f},   // ADC_SENSOR_ELEC_VOLTAGE_VEL
		{0.0f, 1.0f},   // ADC_FC_CURRENT_CFC

		{0.0f, 1.0f},   // ADC_SENSOR_BMS_CURRENT_CBMS
		{0.0f, 11.0f}    // ADC_SENSOR_ELEC_CURRENT_CEL
	},
	CALIBRATION_MAGIC_VALUE
};

// Global variables
CalibrationData calibration_data;
MovingAverage sensorFilters[CAL_SENSOR_COUNT];
PredictiveBuffer waterTempBuffer, elecTempBuffer, bmsTempBuffer;
Adafruit_ADS1115 ads1; // I2C address 0x48
Adafruit_ADS1115 ads2; // I2C address 0x49
Adafruit_ADS1115 ads3; // I2C address 0x4A
Adafruit_ADS1115 ads4; // I2C address 0x4B
ADS1115Reader adsReaders[4];

PZEM004Tv30 pzems[NUM_PZEMS] = {
	PZEM004Tv30(&PZEM_SERIAL, 1),
	PZEM004Tv30(&PZEM_SERIAL, 2),
	PZEM004Tv30(&PZEM_SERIAL, 3)
};
ACS712Meter _currentSensorBMS(ADC_SENSOR_BMS_CURRENT_CBMS);
ACS712Meter _currentSensorFC(ADC_FC_CURRENT_CFC);

VoltageSensor _voltageSensorBMS(
	ADC_SENSOR_BMS_VOLTAGE_VBMS,
	//VOLTAGE_SENSOR_PIN_BMS,
	VOLTAGE_DIV_R1_BMS,
	VOLTAGE_DIV_R2_BMS,
	ADC_REF_VOLTAGE_BMS);

VoltageSensor _voltageSensorFC(
	ADC_FC_VOLTAGE_VFC,
	//VOLTAGE_SENSOR_PIN_FC,
	VOLTAGE_DIV_R1_FC,
	VOLTAGE_DIV_R2_FC,
	ADC_REF_VOLTAGE_FC);

// Sensor mapping - Maps each analog sensor to its ADS1115 module and channel
const SensorMapping sensorMap[14] = {
	// ADS1 channels (sensors 0-3)
	{&ads1, 0, 0}, // ADC_SENSOR_WATER_LEVEL_LS_01
	{&ads1, 1, 0}, // ADC_SENSOR_WATER_TEMP_TT_01
	{&ads1, 2, 0}, // ADC_SENSOR_WATER_PRESS_PT_01
	{&ads1, 3, 0}, // ADC_SENSOR_ELEC_TEMP_TT_02

	// ADS2 channels (sensors 4-7)
	{&ads2, 0, 1}, // ADC_SENSOR_H2_TANK_PRESS_PT_02
	{&ads2, 1, 1}, // ADC_SENSOR_H2_TANK_TEMP_TT_03
	{&ads2, 2, 1}, // ADC_SENSOR_O2_TANK_PRESS_PT_03
	{&ads2, 3, 1}, // ADC_SENSOR_O2_TANK_TEMP_TT_04

	// ADS3 channels (sensors 8-11)
	{&ads3, 0, 2}, // ADC_FC_VOLTAGE_VFC
	{&ads3, 1, 2}, // ADC_SENSOR_BMS_VOLTAGE_VBMS
	{&ads3, 2, 2}, // ADC_SENSOR__VOLTAGE_VEL
	{&ads3, 3, 2}, // ADC_FC_CURRENT_CFC

	// ADS4 channels (sensors 12-13)
	{&ads4, 0, 3}, // ADC_SENSOR_BMS_CURRENT_CBMS
	{&ads4, 1, 3}  // ADC_SENSOR_ELEC_CURRENT_CEL
};

// MovingAverage implementation
MovingAverage::MovingAverage() : sum(0), index(0) {
	memset(readings, 0, sizeof(readings));
}

//Update the filter with new value and return the average
float MovingAverage::update(float newValue) {
	//Subtract oldest sample, add new sample
	sum -= readings[index];
	readings[index] = newValue;
	sum += newValue;

	//Advance index circularly
	index = (index + 1) % SIZE;

	//Increase count up to SIZE
	if (count < SIZE) count++;

	//Avoid division by zero just in case
	return (count > 0) ? (sum / count) : 0.0f;
}

//Initialize the filter with preset value (fill the buffer)
void MovingAverage::init(float value) {
	for (uint8_t i = 0; i < SIZE; i++) {
		readings[i] = value;
	}
	sum = value * SIZE;
	index = 0;
	count = SIZE;
}

// PredictiveBuffer implementation
PredictiveBuffer::PredictiveBuffer() : index(0), count(0) {
	memset(history, 0, sizeof(history));
}

void PredictiveBuffer::addSample(float sample) {
	history[index++] = sample;
	if (index >= PREDICTIVE_SAMPLES) index = 0;
	if (count < PREDICTIVE_SAMPLES) count++;
}

// Initialize sensors
void initializeSensors() {
	// Load calibration data
	loadCalibrationFromEEPROM();

	// Skip ADS1115 initialization in simulation mode
	if (IsI2CSensorsOn && !simulationMode)
	{
		// _currentSensorEL.calibrate(500);
		// _currentSensorFC.calibrate(500);
		// Serial.println(F("System initialization _currentSensorEL..."));

		// Serial.println(F("System initialization ADS1115 begin..."));
		// Initialize ADS1115 modules
		ads1.begin(0x4A);//SDA
		// Serial.println(F("System initialization ADS1115 1..."));
		ads2.begin(0x4B);//SCL
		// Serial.println(F("System initialization ADS1115 2..."));
		ads3.begin(0x48);//GND
		// Serial.println(F("System initialization ADS1115 3..."));
		ads4.begin(0x49);//VCC
		// Serial.println(F("System initialization ADS1115 4..."));

		// Configure ADS settings
		ads1.setGain(ADS_GAIN);
		ads2.setGain(ADS_GAIN);
		ads3.setGain(ADS_GAIN);
		ads4.setGain(ADS_GAIN);

		ads1.setDataRate(ADS_SPS);
		ads2.setDataRate(ADS_SPS);
		ads3.setDataRate(ADS_SPS);
		ads4.setDataRate(ADS_SPS);

		// Configure alert pins
		pinMode(ADS1_ALERT_PIN, INPUT_PULLUP);
		pinMode(ADS2_ALERT_PIN, INPUT_PULLUP);
		pinMode(ADS3_ALERT_PIN, INPUT_PULLUP);
		pinMode(ADS4_ALERT_PIN, INPUT_PULLUP);

		// Initialize ADS readers
		adsReaders[0] = { &ads1, ADS1_ALERT_PIN, {0, 0, 0, 0}, 0 };
		adsReaders[1] = { &ads2, ADS2_ALERT_PIN, {0, 0, 0, 0}, 0 };
		adsReaders[2] = { &ads3, ADS3_ALERT_PIN, {0, 0, 0, 0}, 0 };
		adsReaders[3] = { &ads4, ADS4_ALERT_PIN, {0, 0, 0, 0}, 0 };

		// Serial.println(F("System initialization ADS1115..."));
		// Start first conversion on each ADS and get initial readings
		updateADSReadings();

		// Wait for stable readings
		delay(100);
		updateADSReadings();

		// // Initialize PZEM modules
		// for (int i = 0; i < NUM_PZEMS; i++) {
		//   pzems[i] = PZEM004Tv30(&PZEM_SERIAL, i + 1);
		// }
		// Serial.println(F("System initialization PZEM..."));

		// Initialize sensor filters with actual sensor readings to prevent soft start
		initializeSensorFiltersWithRealValues();
	}
	else if (simulationMode) {
		// Initialize sensor filters for simulation mode with simulation values
		initializeSensorFiltersWithRealValues();
	}
	// Initialize EL Regulator
	elRegulator.begin();
}

// Initialize sensor filters with actual sensor readings to prevent soft start
void initializeSensorFiltersWithRealValues() {
	logMessage(LOG_INFO, F("Initializing sensor filters with real values to prevent soft start"));

	// Mapping of calibration indices to their corresponding sensor readings
	// This ensures each filter gets initialized with its actual sensor value
	struct SensorInitMapping {
		uint8_t calIndex;
		uint8_t sensorIndex;  // For ADS sensors
		bool isADS;           // true for ADS sensors, false for analog pins
		uint8_t analogPin;    // For analog pin sensors
	};

	// Define the mapping between calibration indices and sensor sources
	const SensorInitMapping sensorInitMap[] = {
		{CAL_WATER_LEVEL,    ADC_SENSOR_WATER_LEVEL_LS_01,     true,  0},
		{CAL_WATER_TEMP,     ADC_SENSOR_WATER_TEMP_TT_01,      true,  0},
		{CAL_WATER_PRESS,    ADC_SENSOR_WATER_PRESS_PT_01,     true,  0},
		{CAL_ELEC_TEMP,      ADC_SENSOR_ELEC_TEMP_TT_02,       true,  0},
		{CAL_H2_TANK_PRESS,  ADC_SENSOR_H2_TANK_PRESS_PT_02,   true,  0},
		{CAL_H2_TANK_TEMP,   ADC_SENSOR_H2_TANK_TEMP_TT_03,    true,  0},
		{CAL_O2_TANK_PRESS,  ADC_SENSOR_O2_TANK_PRESS_PT_03,   true,  0},
		{CAL_O2_TANK_TEMP,   ADC_SENSOR_O2_TANK_TEMP_TT_04,    true,  0},
		{CAL_FC_VOLTAGE,     ADC_FC_VOLTAGE_VFC,               true,  0},
		{CAL_BMS_VOLTAGE,    ADC_SENSOR_BMS_VOLTAGE_VBMS,      true,  0},
		{CAL_ELEC_VOLTAGE,   ADC_SENSOR_ELEC_VOLTAGE_VEL,      true,  0},
		{CAL_FC_CURRENT,     ADC_FC_CURRENT_CFC,               true,  0},
		{CAL_BMS_CURRENT,    ADC_SENSOR_BMS_CURRENT_CBMS,      true,  0},
		{CAL_ELEC_CURRENT,   ADC_SENSOR_ELEC_CURRENT_CEL,      true,  0}
	};

	const uint8_t numMappings = sizeof(sensorInitMap) / sizeof(SensorInitMapping);

	// Initialize each sensor filter with its actual reading
	for (uint8_t i = 0; i < numMappings && i < CAL_SENSOR_COUNT; i++) {
		const SensorInitMapping& mapping = sensorInitMap[i];
		float initialValue = 0.0f;

		if (simulationMode) {
			// Use simulation values
			if (mapping.isADS && mapping.sensorIndex < NUM_ANALOG_SENSORS) {
				initialValue = adsSensors[mapping.sensorIndex];
			}
		} else {
			// Read actual sensor values
			if (mapping.isADS) {
				// Get raw ADS reading and convert to voltage
				const SensorMapping& sensorMap_entry = sensorMap[mapping.sensorIndex];
				int adsIndex = -1;
				if (sensorMap_entry.module == &ads1) adsIndex = 0;
				else if (sensorMap_entry.module == &ads2) adsIndex = 1;
				else if (sensorMap_entry.module == &ads3) adsIndex = 2;
				else if (sensorMap_entry.module == &ads4) adsIndex = 3;

				if (adsIndex >= 0) {
					int16_t raw = adsReaders[adsIndex].readings[sensorMap_entry.channel];
					raw = constrain(raw, 0, 32767);
					initialValue = raw * (ADS_FULL_SCALE / 32767.0);
				}
			} else {
				// Read analog pin directly
				int raw = analogRead(mapping.analogPin);
				raw = constrain(raw, 0, 1023);
				initialValue = raw * (5.0f / 1023.0f);
			}
		}

		// Initialize the filter with the actual reading
		sensorFilters[mapping.calIndex].init(initialValue);

		// Log the initialization for debugging - use simpler Serial.print to avoid corruption
		Serial.print(F("Sensor filter init: Filter["));
		Serial.print(mapping.calIndex);
		Serial.print(F("] initialized with value: "));
		Serial.println(initialValue, 3);
	}

	logMessage(LOG_INFO, F("Sensor filter initialization complete - no soft start"));

	// Print all calibrated sensor values for debugging
	printAllSensorValues();
}

// Debug function to print all sensor values
void printAllSensorValues() {
	Serial.println(F("=== SENSOR VALUES DEBUG ==="));
	Serial.print(F("Simulation Mode: ")); Serial.println(simulationMode ? "TRUE" : "FALSE");

	// Temperature sensors
	float waterTemp = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_TEMP_TT_01, CAL_WATER_TEMP);
	float elecTemp = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP_TT_02, CAL_ELEC_TEMP);
	float h2TankTemp = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_TEMP_TT_03, CAL_H2_TANK_TEMP);
	float o2TankTemp = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_TEMP_TT_04, CAL_O2_TANK_TEMP);

	Serial.print(F("Water Temp (TT-01): ")); Serial.print(waterTemp, 1); Serial.println(F("°C"));
	Serial.print(F("Elec Temp (TT-02): ")); Serial.print(elecTemp, 1); Serial.println(F("°C"));
	Serial.print(F("H2 Tank Temp (TT-03): ")); Serial.print(h2TankTemp, 1); Serial.println(F("°C"));
	Serial.print(F("O2 Tank Temp (TT-04): ")); Serial.print(o2TankTemp, 1); Serial.println(F("°C"));

	// Pressure sensors
	float waterPress = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_PRESS_PT_01, CAL_WATER_PRESS);
	float h2TankPress = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_PRESS_PT_02, CAL_H2_TANK_PRESS);
	float o2TankPress = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_PRESS_PT_03, CAL_O2_TANK_PRESS);

	Serial.print(F("Water Press (PT-01): ")); Serial.print(waterPress, 1); Serial.println(F(" bar"));
	Serial.print(F("H2 Tank Press (PT-02): ")); Serial.print(h2TankPress, 1); Serial.println(F(" bar"));
	Serial.print(F("O2 Tank Press (PT-03): ")); Serial.print(o2TankPress, 1); Serial.println(F(" bar"));

	// Level sensor
	float waterLevel = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL_LS_01, CAL_WATER_LEVEL);
	Serial.print(F("Water Level (LS-01): ")); Serial.print(waterLevel, 1); Serial.println(F("%"));

	// Check temperature safety thresholds
	bool tempOK = (waterTemp >= 15.0f && waterTemp <= 55.0f &&
				   elecTemp >= 15.0f && elecTemp <= 55.0f &&
				   h2TankTemp >= 15.0f && h2TankTemp <= 55.0f &&
				   o2TankTemp >= 15.0f && o2TankTemp <= 55.0f);

	Serial.print(F("Temperature Safety Check: ")); Serial.println(tempOK ? "PASS" : "FAIL");
	Serial.println(F("=== END SENSOR DEBUG ==="));
}

// --- MUX constants for single-ended channels 0–3 ---
const uint16_t muxSingle[4] = {
   ADS1X15_REG_CONFIG_MUX_SINGLE_0,
   ADS1X15_REG_CONFIG_MUX_SINGLE_1,
   ADS1X15_REG_CONFIG_MUX_SINGLE_2,
   ADS1X15_REG_CONFIG_MUX_SINGLE_3
};

// Update ADS readings
void updateADSReadings() {
	// Skip ADS operations in simulation mode
	if (IsI2CSensorsOn && !simulationMode)
	{
		// if (!simulationMode) {
		//     // ADS1115 #0: Read all 4 channels
		//     adsReaders[0].readings[0] = adsReaders[0].ads->readADC_SingleEnded(0);
		//     adsReaders[0].readings[1] = adsReaders[0].ads->readADC_SingleEnded(1);
		//     adsReaders[0].readings[2] = adsReaders[0].ads->readADC_SingleEnded(2);
		//     adsReaders[0].readings[3] = adsReaders[0].ads->readADC_SingleEnded(3);

		//     // ADS1115 #1: Read all 4 channels
		//     adsReaders[1].readings[0] = adsReaders[1].ads->readADC_SingleEnded(0);
		//     adsReaders[1].readings[1] = adsReaders[1].ads->readADC_SingleEnded(1);
		//     adsReaders[1].readings[2] = adsReaders[1].ads->readADC_SingleEnded(2);
		//     adsReaders[1].readings[3] = adsReaders[1].ads->readADC_SingleEnded(3);

		//     // ADS1115 #2: Read all 4 channels
		//     adsReaders[2].readings[0] = adsReaders[2].ads->readADC_SingleEnded(0);
		//     adsReaders[2].readings[1] = adsReaders[2].ads->readADC_SingleEnded(1);
		//     adsReaders[2].readings[2] = adsReaders[2].ads->readADC_SingleEnded(2);
		//     adsReaders[2].readings[3] = adsReaders[2].ads->readADC_SingleEnded(3);

		//     // ADS1115 #3: Read only channels 0 and 1
		//     adsReaders[3].readings[0] = adsReaders[3].ads->readADC_SingleEnded(0);
		//     adsReaders[3].readings[1] = adsReaders[3].ads->readADC_SingleEnded(1);
		//     // // Optionally clear channels 2 and 3
		//     // adsReaders[3].readings[2] = 0;
		//     // adsReaders[3].readings[3] = 0;
		//   }
		// }

		// For each channel index 0–3, pipeline conversions across all ADS chips:
		for (uint8_t ch = 0; ch < 4; ++ch) {
			// 1) Start a single-shot conversion on that channel for each chip
			for (uint8_t i = 0; i < 4; ++i) {
				// Board #3 only has channels 0 and 1 in use
				if (i == 3 && ch >= 2) break;
				adsReaders[i].ads->startADCReading(muxSingle[ch], /*continuous=*/false);
			}

			// 2) Wait until the conversion on chip #0 completes
			// (By now, all boards started at roughly the same time)
			while (!adsReaders[0].ads->conversionComplete()) {
				delayMicroseconds(50);
			}

			// 3) Fetch the results from each chip for this channel
			for (uint8_t i = 0; i < 4; ++i) {
				if (i == 3 && ch >= 2) {
					// Not used on board #3
					adsReaders[i].readings[ch] = 0;
				}
				else {
					adsReaders[i].readings[ch] = adsReaders[i].ads->getLastConversionResults();
				}
			}
		}
	}
}



// Get filtered and calibrated value from ADS
float getFilteredCalibratedValueADS(uint8_t sensor, uint8_t calIndex)
{
	if (simulationMode)
	{
		// In simulation mode, return the simulation values directly (already calibrated)
		float simulated = adsSensors[sensor];
		float filtered = sensorFilters[calIndex].update(simulated);
		return filtered;  // No calibration needed - simulation values are final values
	}

	const SensorMapping& mapping = sensorMap[sensor];
	int adsIndex = -1;
	if (mapping.module == &ads1) adsIndex = 0;
	else if (mapping.module == &ads2) adsIndex = 1;
	else if (mapping.module == &ads3) adsIndex = 2;
	else if (mapping.module == &ads4) adsIndex = 3;

	int16_t raw = 0;
	if (adsIndex >= 0) {
		raw = adsReaders[adsIndex].readings[mapping.channel];
	}

	raw = constrain(raw, 0, 32767);
	float scaledValue = raw * (ADS_FULL_SCALE / 32767.0);

	// Serial.print(sensor);
	// Serial.println(voltage);
	float filtered = sensorFilters[calIndex].update(scaledValue);

	return (filtered * calibration_data.sensors[calIndex].scale)
		+ calibration_data.sensors[calIndex].offset;
}

// Get filtered and calibrated value from analog pin
float getFilteredCalibratedValue(uint8_t analogPin, uint8_t calIndex) {
	if (simulationMode) {
		uint8_t index = analogPin - A0;
		float simulated = analogSensors[index];
		float filtered = sensorFilters[calIndex].update(simulated);
		return (filtered * calibration_data.sensors[calIndex].scale)
			+ calibration_data.sensors[calIndex].offset;
	}

	int raw = analogRead(analogPin);
	raw = constrain(raw, 0, 1023);
	float voltage = raw * (5.0f / 1023.0f); // Convert to 0-5V for Arduino Mega 2560
	float filtered = sensorFilters[calIndex].update(voltage);

	return (filtered * calibration_data.sensors[calIndex].scale)
		+ calibration_data.sensors[calIndex].offset;
}

// Calculate slope for predictive analysis
float calculate_slope(const float data[], uint8_t length, float dt) {
	if (length < 2) return 0.0f;

	float sum_x = 0, sum_y = 0, sum_xy = 0, sum_x2 = 0;
	for (uint8_t i = 0; i < length; i++) {
		float x = i * dt;
		float y = data[i];
		sum_x += x;
		sum_y += y;
		sum_xy += x * y;
		sum_x2 += x * x;
	}

	float N = length;
	float denom = (N * sum_x2 - sum_x * sum_x);
	if (fabs(denom) < 1e-6f) return 0.0f;

	return (N * sum_xy - sum_x * sum_y) / denom;
}

// PZEM helper functions
float getPZEMVoltage(uint8_t i) {
	return simulationMode ? pzemModel[i].voltage : pzems[i].voltage();
}

float getPZEMCurrent(uint8_t i) {
	return simulationMode ? pzemModel[i].current : pzems[i].current();
}

float getPZEMPower(uint8_t i) {
	return simulationMode ? pzemModel[i].power : pzems[i].power();
}

float getPZEMEnergy(uint8_t i) {
	return simulationMode ? pzemModel[i].energy : pzems[i].energy();
}

float getPZEMFrequency(uint8_t i) {
	return simulationMode ? pzemModel[i].frequency : pzems[i].frequency();
}

float getPZEMPF(uint8_t i) {
	return simulationMode ? pzemModel[i].pf : pzems[i].pf();
}

float getCurrent_EL()
{
	return simulationMode ? currentACS_EL_SimValue : getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_CURRENT_CEL, CAL_ELEC_CURRENT);
}

float getACSCurrent_BMS()
{
	return simulationMode ? currentACS_BMS_SimValue : _currentSensorBMS.readCurrent(100);
}

float getACSCurrent_FC()
{
	return simulationMode ? currentACS_FC_SimValue : _currentSensorFC.readCurrent(100);
}

float getVolt_EL()
{
	//return simulationMode ? volt_EL_SimValue : elRegulator.readSupplyVoltage();
	return simulationMode ? volt_EL_SimValue : getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_VOLTAGE_VEL, CAL_ELEC_VOLTAGE);//  _voltageSensorEL.readVoltage();
}
float getVolt_BMS()
{
	return simulationMode ? volt_BMS_SimValue : _voltageSensorBMS.readVoltage();
}
float getVolt_FC()
{
	return simulationMode ? volt_FC_SimValue : _voltageSensorFC.readVoltage();
}

// Helper function to check if a pin is a power relay pin
bool IsPowerRelayPin(uint8_t pin) {
	for (uint8_t i = 0; i < sizeof(POWER_RELAY_PINS) / sizeof(POWER_RELAY_PINS[0]); i++) {
		if (pin == POWER_RELAY_PINS[i]) return true;
	}
	return false;
}

// Digital I/O wrapper functions
void SetDigitalOutputVal(uint8_t pin, uint8_t value) {
	if (simulationMode) {
		// For actuator outputs (pins ACTUATOR_PIN_START to ACTUATOR_PIN_END), use the boolean array.
		if (pin >= ACTUATOR_PIN_START && pin < ACTUATOR_PIN_END) {
			if (IsPowerRelayPin(pin))
			{
				simDigitalOutputs[pin - ACTUATOR_PIN_START] = (value == LOW);//TURN OFF ACTUATORS
			}
			else
			{
				simDigitalOutputs[pin - ACTUATOR_PIN_START] = (value == HIGH);//TURN OFF ACTUATORS
			}
		}
	}
	else
	{
		if (pin >= ACTUATOR_PIN_START && pin < ACTUATOR_PIN_END) {
			if (IsPowerRelayPin(pin))
			{
				digitalWrite(pin, (value == LOW));//TURN ON ACTUATORS
			}
			else
			{
				digitalWrite(pin, (value == HIGH));//TURN OFF ACTUATORS
			}
		}
	}
}



//1 com 2 no 3 nc
uint8_t GetLevelSwitchInputVal(uint8_t pin)
{
	if (simulationMode) {
		// Return simulation values for analog level switches
		if (pin == PIN_ELEC_O2_DRYER_LEVEL_LS_02)
			return simAnalogInputs.elecO2DryerLevel;
		if (pin == PIN_ELEC_H2_DRYER_LEVEL_LS_03)
			return simAnalogInputs.elecH2DryerLevel;
		if (pin == PIN_FC_O2_DRYER_LEVEL_LS_04)
			return simAnalogInputs.fcO2DryerLevel;
		return 0; // Default for unknown pins
	}

	uint8_t val = analogRead(pin);
	if (val < 100)
	{
		return 10;//Min : 0
	}
	else if (val > 900)
	{
		return 100;//Max : 1
	}
	else
	{
		return 0;//Boş : -1
	}
}

uint8_t GetDigitalInputVal(uint8_t pin)
{
	if (simulationMode) {
		/* if (pin == PIN_ELEC_O2_DRYER_LEVEL_LS_02)
		   return simDigitalInputs.elecO2DryerLevel ? HIGH : LOW;
		 if (pin == PIN_ELEC_H2_DRYER_LEVEL_LS_03)
		   return simDigitalInputs.elecH2DryerLevel ? HIGH : LOW;
		 if (pin == PIN_FC_O2_DRYER_LEVEL_LS_04)
		   return simDigitalInputs.fcO2DryerLevel ? HIGH : LOW;*/

		   //TODO: SET
		if (pin == PIN_SENSOR_FIRE_DETECT_FS_01)
			return simDigitalInputs.sensorFireDetect ? HIGH : LOW;
		if (pin == PIN_SENSOR_H2_AMBIENT_HS_01)
			return simDigitalInputs.sensorH2Ambient ? HIGH : LOW;
		if (pin == PIN_EMERGENCY_STOP_ES_01)
			return simDigitalInputs.buttonEmergencyStop ? HIGH : LOW;

		// Default for unknown pins in simulation mode
		return HIGH;
	}
	else
	{
		if (IsPowerRelayPin(pin))
		{
			return !digitalRead(pin);
		}
		else
		{
			return digitalRead(pin);
		}
	}
}

uint8_t GetDigitalOutputVal(uint8_t pin)
{
	if (simulationMode) {
		if (pin >= ACTUATOR_PIN_START && pin < ACTUATOR_PIN_END)
		{
			if (IsPowerRelayPin(pin))
			{
				return simDigitalOutputs[pin - ACTUATOR_PIN_START] ? LOW : HIGH;
			}
			else
			{
				return simDigitalOutputs[pin - ACTUATOR_PIN_START] ? HIGH : LOW;
			}
		}
	}
	else
	{
		if (IsPowerRelayPin(pin))
		{
			return !digitalRead(pin);
		}
		else
		{
			return digitalRead(pin);
		}
	}
	return LOW;
}


// Load calibration from EEPROM
void loadCalibrationFromEEPROM() {
	//saveCalibrationToEEPROM();
	EEPROM.get(0, calibration_data);

	// Validate calibration data
	if (calibration_data.magic != CALIBRATION_MAGIC_VALUE ||
		calibration_data.version != CALIBRATION_VERSION) {
		// Use default calibration if invalid
		calibration_data = default_calibration;
		saveCalibrationToEEPROM();
		Serial.println(F("loadCalibrationFromEEPROM..."));
	}
}

// Save calibration to EEPROM
void saveCalibrationToEEPROM() {
	EEPROM.put(0, calibration_data);
	Serial.println(F("saveCalibrationToEEPROM..."));
}
