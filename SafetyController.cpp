#include "SafetyController.h"
#include "SystemController.h"
#include "ElectrolyzerController.h"
#include "FuelCellController.h"
#include "ELRegulator.h"

// Global SafetyController instance
SafetyController safety_controller;

extern SystemController system_controller;
extern ELRegulator elRegulator;

// Map fault bits to names
static const struct { uint8_t mask; const char* name; } FaultMap[] = {
	{ SafetyController::FAULT_SELFTEST_FAIL, "SELFTEST_FAIL" },
	{ SafetyController::FAULT_UNDERPRESSURE, "UNDERPRESSURE" },
	{ SafetyController::FAULT_OVERPRESSURE, "OVERPRESSURE" },
	{ SafetyController::FAULT_OVERTEMP, "OVERTEMP" },
	{ SafetyController::FAULT_LOW_WATER_LEVEL, "LOW_WATER_LEVEL" },
	{ SafetyController::FAULT_H2_LEAK, "H2_LEAK" },
	{ SafetyController::FAULT_COMMS_LOSS, "COMMS_LOSS" }
};

SafetyController::SafetyController()
	: activeFaults(0),
	lastCheckTs(0),
	lastCommTs(millis())
{
}


// Return comma-separated list of active fault names
String SafetyController::formatFaultNames(uint8_t faults) const {
	String list;
	for (auto& fm : FaultMap) {
		if (faults & fm.mask) {
			if (list.length()) list += ", ";
			list += fm.name;
		}
	}
	if (!list.length()) list = "None";
	return list;
}



void SafetyController::update() {
	unsigned long now = millis();
	if (now - lastCheckTs < Constants::CONTROL_LOOP_FREQ_MS) return;
	lastCheckTs = now;

	if (isEmergency())
		return;

	// Start fresh but keep any self-test failures latched
	uint8_t newF = activeFaults & FAULT_SELFTEST_FAIL;

	// 1) Water pressure (PT-01)
	float pt01 = getFilteredCalibratedValueADS(
		ADC_SENSOR_WATER_PRESS_PT_01, CAL_WATER_PRESS);

	if (system_controller.get_mode() == SystemController::MODE_ELECTROLYZER)
	{
		// Water pressure should be positive and within operating range
		if (pt01 < 0.0f) newF |= FAULT_UNDERPRESSURE;
	}
	if (pt01 > Constants::WATER_PRESSURE_STOP) newF |= FAULT_OVERPRESSURE;


	// 2) Tank pressures (PT-02 & PT-03)
	// REDeS system uses 1-bar offset sensors: 0.5 bar reading = sea level
	// Valid range is 0.5-4 bar (sensor reads 0.5 at sea level, 4.5 at max pressure)
	float pt02 = getFilteredCalibratedValueADS(
		ADC_SENSOR_H2_TANK_PRESS_PT_02, CAL_H2_TANK_PRESS);
	float pt03 = getFilteredCalibratedValueADS(
		ADC_SENSOR_O2_TANK_PRESS_PT_03, CAL_O2_TANK_PRESS);
	if (system_controller.get_mode() == SystemController::MODE_FUEL_CELL)
	{
		// Check for sensor failure (extremely low readings) or overpressure
		if (pt02 < 0.3f || pt03 < 0.3f) newF |= FAULT_UNDERPRESSURE;  // Below reasonable sensor range
	}
	if (pt02 > Constants::MAX_TANK_PRESSURE_BAR ||
		pt03 > Constants::MAX_TANK_PRESSURE_BAR) newF |= FAULT_OVERPRESSURE;

	// 3) Temperatures TT01–TT04 - Use Constants instead of hard-coded values
	auto badTemp = [&](uint8_t sensor, uint8_t cal) {
		float t = getFilteredCalibratedValueADS(sensor, cal);
		return (t < Constants::WATER_TEMP_LOW || t > Constants::WATER_TEMP_HIGH);
		};
	if (badTemp(ADC_SENSOR_WATER_TEMP_TT_01, CAL_WATER_TEMP) ||
		badTemp(ADC_SENSOR_ELEC_TEMP_TT_02, CAL_ELEC_TEMP) ||
		badTemp(ADC_SENSOR_H2_TANK_TEMP_TT_03, CAL_H2_TANK_TEMP) ||
		badTemp(ADC_SENSOR_O2_TANK_TEMP_TT_04, CAL_O2_TANK_TEMP))
	{

		float valWaterTemp = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_TEMP_TT_01, CAL_WATER_TEMP);
		float valElecTemp = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP_TT_02, CAL_ELEC_TEMP);
		float valH2TankTemp = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_TEMP_TT_03, CAL_H2_TANK_TEMP);
		float valO2TankTemp = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_TEMP_TT_04, CAL_O2_TANK_TEMP);
		char logBuf[100];

	/*	snprintf(logBuf,
			sizeof(logBuf),
			"Water Temp=%.1f Elec Temp=%.1f H2 Tank Temp=%.1f O2 Tank Temp=%.1f",
			valWaterTemp, valElecTemp, valH2TankTemp, valO2TankTemp);*/

		String payload = String("Water Temp=") + String(valWaterTemp, 1)
			+ " Elec Temp=" + String(valElecTemp, 1)
			+ " H2 Tank Temp=" + String(valH2TankTemp, 1)
			+ " O2 Tank Temp=" + String(valO2TankTemp, 1);

		logMessage(LOG_ERROR, F("Safety:FAULT_OVERTEMP"), payload);
		//logMessage(LOG_ERROR,F("Safety:FAULT_OVERTEMP "), String(logBuf));

		//Todo: check at startup
		newF |= FAULT_OVERTEMP;
	}


	if (system_controller.get_mode() == SystemController::MODE_ELECTROLYZER)
	{
		// 4) Water level (LS-01)
		float lvl01 = getFilteredCalibratedValueADS(
			ADC_SENSOR_WATER_LEVEL_LS_01, CAL_WATER_LEVEL);
		if (lvl01 < Constants::WATER_LEVEL_MIN) newF |= FAULT_LOW_WATER_LEVEL;
	}

	// 5) Critical safety sensors
	// Fire detection sensor (FS-01) - indicates fire detected
	if (GetDigitalInputVal(PIN_SENSOR_FIRE_DETECT_FS_01) == LOW) {
		newF |= FAULT_H2_LEAK;  // Use H2_LEAK fault for fire detection
		logMessage(LOG_ERROR, F("Safety: Fire detected by FS-01!"));
	}

	// H2 ambient sensor (HS-01) - indicates H2 leak detected
	if (GetDigitalInputVal(PIN_SENSOR_H2_AMBIENT_HS_01) == LOW) {
		newF |= FAULT_H2_LEAK;
		logMessage(LOG_ERROR, F("Safety: H2 leak detected by HS-01!"));
	}

	//TODO: Check relays
	if (system_controller.get_mode() == SystemController::MODE_SAFE)
	{
		lastBMSCheckTs = now;
		if (now - lastBMSCheckTs > Constants::BMSCONTROL_LOOP_FREQ_MS)
		{
			// Check BMS status before enabling battery power
			float battVolt = getFilteredCalibratedValueADS(ADC_SENSOR_BMS_VOLTAGE_VBMS, CAL_BMS_VOLTAGE);

			if (battVolt >= Constants::BATT_MAX_VOLTAGE_V) {
				logMessage(LOG_ERROR, F("Safety: Battery Voltage Too High"));
				newF |= FAULT_SELFTEST_FAIL;
			}
			else if (battVolt >= Constants::DISCHARGE_OFF_THRESHOLD
				&& GetDigitalOutputVal(ACT_BMS_CHARGE_RELAY) == LOW) {
				logMessage(LOG_ERROR, F("Safety: Battery Voltage Low"));
			}
			else {
				if (battVolt < Constants::CHARGE_OFF_THRESHOLD)
				{
					if (GetDigitalInputVal(ACT_MAIN_INPUT_RELAY != HIGH)
						|| GetDigitalInputVal(ACT_BMS_CHARGE_RELAY != HIGH))
					{
						SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, HIGH);

						SetDigitalOutputVal(ACT_BMS_CHARGE_RELAY, HIGH);
						logMessage(LOG_ERROR, F("Safety: Battery is Low. Started charging battery"));
					}
				}
				else
				{
					if (GetDigitalInputVal(ACT_BMS_CHARGE_RELAY != LOW))
					{
						//SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);
						SetDigitalOutputVal(ACT_BMS_CHARGE_RELAY, LOW);
						logMessage(LOG_ERROR, F("Safety: Battery is full. Stopped charging battery"));
					}
				}
			}
		}


	}

	//TODO: CONDITIONAL
	//// 6) Comm watchdog
	//if (now - lastCommTs > Constants::COMM_WATCHDOG_TIMEOUT_MS)
	//	newF |= FAULT_COMMS_LOSS;

	activeFaults = newF;

	// Only trigger emergency shutdown for critical faults or any fault combination
	if (activeFaults && isCriticalFault(activeFaults)) {
		emergency_shutdown();
	}
	else if (activeFaults) {
		// Non-critical faults: log but don't immediately shutdown

		String names = formatFaultNames(activeFaults);
		logMessage(LOG_WARN, F("Safety: Non-critical faults detected: "), names.c_str());

		//String bits = String(activeFaults, HEX);
		//logMessage(LOG_WARN, F("Safety: Non-critical faults detected: "), bits);
	}
}

void SafetyController::reportSubsystemFault(FaultCode f) {
	activeFaults |= f;

	// Check if this fault requires immediate emergency shutdown
	if (isCriticalFault(activeFaults)) {
		emergency_shutdown();
	}
	else {
		// Log non-critical fault
		//String bits = String(activeFaults, HEX);
		//logMessage(LOG_WARN, F("Safety: Subsystem fault reported: "), bits);

		String names = formatFaultNames(activeFaults);
		logMessage(LOG_WARN, F("Safety: Subsystem fault reported: "), names.c_str());
	}
}

void SafetyController::resetCommWatchdog() {
	lastCommTs = millis();
}

void SafetyController::clearFaults() {
	activeFaults = 0;
}

void SafetyController::clearFault(FaultCode f) {
	activeFaults &= ~f;  // Clear specific fault bit
}

bool SafetyController::isCriticalFault(uint8_t faults) const {
	// Critical faults that require immediate emergency shutdown
	uint8_t criticalFaults = FAULT_H2_LEAK | FAULT_OVERPRESSURE | FAULT_OVERTEMP;

	// Always shutdown for critical faults, or if multiple faults are present
	return (faults & criticalFaults) || (__builtin_popcount(faults) > 1);
}

uint8_t SafetyController::getFaultStatus() const {
	return activeFaults;
}

bool SafetyController::isEmergency() const {
	// System is in emergency if there are critical faults or system is in emergency mode
	return isCriticalFault(activeFaults) ||
		system_controller.get_mode() == SystemController::MODE_EMERGENCY;
}

void SafetyController::triggerEmergency() {
	emergency_shutdown();
}


void SafetyController::emergency_shutdown() {
	// Hex string of active bits
   /* String bits = String(activeFaults, HEX);
	logMessage(LOG_ERROR, F("Safety: EMERGENCY shutdown! Faults= "), bits);*/

	String names = formatFaultNames(activeFaults);
	logMessage(LOG_WARN, F("Safety: EMERGENCY shutdown! Faults= "), names.c_str());

	// PRS-7: Automatic shutdown procedure per working procedures
	system_controller.force_mode(SystemController::MODE_EMERGENCY);

	// PRS-7: Reset ALL actuators to startup states (not just safety-related ones)
	// This is necessary because actuators may be manually enabled via PC telemetry

	// All pumps OFF
	SetDigitalOutputVal(ACT_WATER_PUMP_PM_02, LOW);
	SetDigitalOutputVal(ACT_WATER_FILL_PUMP_PM_01, LOW);
	SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP_PM_03, LOW);

	// All valves CLOSED
	SetDigitalOutputVal(ACT_WATER_INLET_VALVE_SV_01, LOW);
	SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE_SV_03, LOW);
	SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE_SV_02, LOW);
	SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, LOW);
	SetDigitalOutputVal(ACT_FC_H2_SUPPLY_VALVE_SV_05, LOW);
	SetDigitalOutputVal(ACT_FC_O2_SUPPLY_VALVE_SV_06, LOW);
	SetDigitalOutputVal(ACT_FC_H2_DISCHARGE_VALVE_SV_07, LOW);

	// All equipment OFF
	SetDigitalOutputVal(ACT_EL_HEATER_EH_01, LOW);
	SetDigitalOutputVal(ACT_EL_O2_CHILLER_CF_01, LOW);
	SetDigitalOutputVal(ACT_FC_O2_CHILLER_CF_02, LOW);
	SetDigitalOutputVal(ACT_FC_O2_FAN_RF_01, LOW);

	// Power systems to safe states
	SetDigitalOutputVal(ACT_EL_PSU_RELAY, LOW);
	SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);
	SetDigitalOutputVal(ACT_FC_LOAD_RELAY, LOW);
	SetDigitalOutputVal(ACT_FC_POWER_RELAY, LOW);
	SetDigitalOutputVal(ACT_BMS_CHARGE_RELAY, LOW);
	SetDigitalOutputVal(ACT_BMS_DISCHARGE_RELAY, LOW);
	SetDigitalOutputVal(ACT_INVERTER_RELAY, LOW);

	// Emergency systems
	SetDigitalOutputVal(ACT_EMERGENCY_VENT, HIGH);  // Emergency vent OPEN for safety
	SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, HIGH);  // Battery power ON for essential systems

	// Turn off electrolyzer regulator
	elRegulator.setOutputVoltage(0.0f);
}




