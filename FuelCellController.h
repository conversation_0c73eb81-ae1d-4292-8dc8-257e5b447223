#ifndef FUELCELL_CONTROLLER_H
#define FUELCELL_CONTROLLER_H

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"

/**
  * FuelCellController
  * Implements:
  *   PRS-4 Startup checks (power, gas)
  *   PRS-5 Run sequence (valves, chiller)
  *   PRS-6 Shutdown sequence
  *   Emergency stop
  */
class FuelCellController {
public:
	enum State {
		IDLE,
		PRS4_POWER_CHECK,
		PRS4_GAS_CHECK,
		PRS5_RUN,
		PRS6_SHUTDOWN,
		STOPPED,
		EMERGENCY
	};

	FuelCellController();
	void begin();                  // call in setup()
	void update();                // call in loop()
	void reset();                  // re-enter PRS4
	void deactivate();            // safe shutdown
	void emergency_stop();    // immediate all-off

	State getState() const { return _state; }

private:
	// raw sensor values
	float _press_PT02;    // H2 tank pressure
	float _press_PT03;    // O2 tank pressure
	float _temp_TT03;      // H2 gas temperature
	float _temp_TT04;      // O2 gas temperature

	State            _state;
	unsigned long _tState;            // timestamp when entered state
	unsigned long _tLastAction;    // debounce/timing
	bool               _sv07Open;         // for timed discharge
	unsigned long _sv07Ts;            // discharge start time

	// LS-04 dryer control (integrated from FuelCellDryerController)
	bool _fcO2DryerActive;
	unsigned long _lastFcO2DryerToggle;

	// Voltage checking during operation
	unsigned long _tRunStart;  // timestamp when PRS5_RUN started

	void readAllSensors();
	void performPRS4();
	void performPRS5();
	void performPRS6();
	void handleFcO2Dryer();  // Handle LS-04 dryer control (integrated from FuelCellDryerController)
	const char* stateToString(State s) const;
};

extern FuelCellController fuel_cell_controller;

#endif // FUELCELL_CONTROLLER_H