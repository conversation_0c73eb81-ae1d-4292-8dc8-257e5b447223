#ifndef SYSTEM_CONTROLLER_H
#define SYSTEM_CONTROLLER_H

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

// Forward declarations to avoid circular dependencies
class ElectrolyzerController;
class FuelCellController;
class ELRegulator;
class SafetyController;

class SystemController
{
public:
	enum OperationMode : uint8_t
	{
		MODE_SAFE = 0,
		MODE_ELECTROLYZER,
		MODE_FUEL_CELL,
		MODE_BATTERY_INVERTER,
		MODE_SYSTEM_TEST,
		MODE_EMERGENCY,
		MODE_COUNT
	};

	SystemController();

	// Request a validated mode change
	void request_mode(OperationMode m);

	// Call this every loop to drive transitions
	void update();

	OperationMode get_mode() const { return currentMode; }

	// Force immediate mode change (e.g. emergency)
	void force_mode(OperationMode m);

	// Human-readable for logs/UI
	static const __FlashStringHelper* getModeName(OperationMode m);

private:
	OperationMode currentMode;
	OperationMode pendingMode;
	bool inTransition;
	unsigned long transitionStartTs;
	enum SubState
	{
		TRANS_IDLE,
		TRANS_VERIFY,
		TRANS_DONE
	} subState;
	unsigned long stepStartTs;

	bool validate_transition(OperationMode m);
	void begin_transition(OperationMode m);
	void complete_transition();
	void deactivate_current_mode();
	void activate_pending_mode();

	void activateElectrolyzer();
	void activateFuelCell();
	void activateBatteryInverter();
	void activateSystemTest();
};

extern SystemController system_controller;

#endif // SYSTEM_CONTROLLER_H